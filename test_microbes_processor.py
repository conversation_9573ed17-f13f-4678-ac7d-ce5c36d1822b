#!/usr/bin/env python3
"""
测试微生物数据处理脚本的修改
"""
import sys
import os
from pathlib import Path

# 添加data目录到Python路径
sys.path.append(str(Path(__file__).parent / "data"))

from process_microbes_data import MicrobesDataProcessor

def test_taxonomy_processing():
    """测试分类信息处理（特别是空值处理）"""
    print("=== 测试分类信息处理 ===")
    
    processor = MicrobesDataProcessor()
    
    # 模拟一个taxonomy数据
    test_taxonomy_data = {
        'OTU1': {
            'kingdom': 'Bacteria',
            'phylum': 'Proteobacteria', 
            'class': 'Gammaproteobacteria',
            'order': '',  # 空值
            'family': '',  # 空值
            'genus': '',   # 空值
            'taxonomy_path': 'd__Bacteria;p__Proteobacteria;c__Gammaproteobacteria;o__;f__;g__',
            'confidence': 0.85
        }
    }
    
    print("原始数据:")
    for level in ['kingdom', 'phylum', 'class', 'order', 'family', 'genus']:
        value = test_taxonomy_data['OTU1'][level]
        print(f"  {level}: '{value}'")
    
    print("\n修改后的处理方式:")
    print("- 空值保持为空，不添加_unclassified后缀")
    print("- 分类在最后一个有效级别停止")
    
def test_directory_structure():
    """测试新的目录结构处理"""
    print("\n=== 测试目录结构处理 ===")
    
    expected_structure = """
    期望的目录结构:
    data/data/
    ├── MPMAID01-001/
    │   ├── 16S/
    │   │   ├── otu_table.tsv
    │   │   └── taxonomy.tsv
    │   └── ITS/
    │       ├── otu_table.tsv
    │       └── taxonomy.tsv
    ├── MPMAID01-002/
    │   ├── 16S/
    │   │   ├── otu_table.tsv
    │   │   └── taxonomy.tsv
    │   └── ITS/
    │       ├── otu_table.tsv
    │       └── taxonomy.tsv
    └── ...
    """
    print(expected_structure)
    
    print("处理逻辑:")
    print("1. 遍历每个项目文件夹 (MPMAID01-xxx)")
    print("2. 在每个项目下查找 16S 和 ITS 子文件夹")
    print("3. 为每个子文件夹生成组合ID: 项目ID_数据类型 (如: MPMAID01-001_16S)")
    print("4. 从组合ID中提取项目ID来获取宿主名称")

def test_host_name_mapping():
    """测试宿主名称映射"""
    print("\n=== 测试宿主名称映射 ===")
    
    processor = MicrobesDataProcessor()
    
    test_cases = [
        "MPMAID01-001_16S",
        "MPMAID01-002_ITS", 
        "MPMAID01-003_16S",
        "001_16S",  # 测试自动添加前缀
    ]
    
    print("测试用例:")
    for data_type_id in test_cases:
        host_name = processor._get_host_name(data_type_id)
        print(f"  {data_type_id} -> {host_name}")

def main():
    """主测试函数"""
    print("微生物数据处理脚本修改测试")
    print("=" * 50)
    
    try:
        test_taxonomy_processing()
        test_directory_structure()
        test_host_name_mapping()
        
        print("\n=== 主要修改总结 ===")
        print("1. ✅ 移除了 _unclassified 后缀的自动添加")
        print("2. ✅ 支持新的目录结构 (项目/16S|ITS/文件)")
        print("3. ✅ 更新了ID处理逻辑以支持数据类型后缀")
        print("4. ✅ 保持了原有的统计和输出功能")
        
        print("\n=== 使用说明 ===")
        print("1. 确保数据目录结构符合新格式")
        print("2. 运行脚本: python data/process_microbes_data.py")
        print("3. 结果将保存到 results/microbes_stats.json")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
