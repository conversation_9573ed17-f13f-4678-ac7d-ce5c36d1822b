#!/usr/bin/env python3
import os
import json
import pandas as pd
import numpy as np
import mysql.connector
from pathlib import Path
from typing import Dict, List, Any
import logging
import sys

# ============= 配置区域 =============
# 输入数据目录路径（包含多个ID子文件夹的目录）
INPUT_DIR = "D:/Soil_microbialData/database-output-after"

# 输出文件路径
OUTPUT_FILE = "results/microbes_stats.json"

# 文件名配置
OTU_FILE_NAME = "otu_table.tsv"
TAXONOMY_FILE_NAME = "taxonomy.tsv"
PLANT_INFO_FILE = "毕业论文数据库数据.xlsx"  # Excel文件路径，包含Study ID和Study Name

# ==================================

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MicrobesDataProcessor:
    def __init__(self):
        self.base_dir = Path(INPUT_DIR)
        self.output_file = Path(OUTPUT_FILE)
        self.all_results: Dict[str, Any] = {}
        self.host_mapping: Dict[str, str] = {}
        
        # 验证输入目录是否存在
        if not self.base_dir.exists():
            raise ValueError(f"输入目录不存在: {self.base_dir}")
        if not self.base_dir.is_dir():
            raise ValueError(f"指定的路径不是目录: {self.base_dir}")
            
        # 加载宿主映射
        self._load_host_mapping()

    def _clean_text(self, text: str) -> str:
        """清理文本中的特殊字符"""
        if not isinstance(text, str):
            return text
        # 移除不可见字符和特殊Unicode字符
        cleaned = ''.join(char for char in text if char.isprintable())
        # 移除多余的空格
        cleaned = ' '.join(cleaned.split())
        return cleaned

    def _validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证并清理数据"""
        validated = {}
        for key, value in data.items():
            if isinstance(value, str):
                validated[key] = self._clean_text(value)
            elif isinstance(value, dict):
                validated[key] = self._validate_data(value)
            elif isinstance(value, list):
                validated[key] = [self._clean_text(x) if isinstance(x, str) else x for x in value]
            else:
                validated[key] = value
        return validated
            
    def _load_host_mapping(self):
        """从Excel文件加载ID到植物名的映射"""
        try:
            logging.info("正在从Excel文件加载植物信息...")
            plant_info_path = Path(PLANT_INFO_FILE)
            
            if not plant_info_path.exists():
                logging.warning(f"植物信息文件不存在: {plant_info_path}")
                return
                
            # 读取Excel文件
            df = pd.read_excel(plant_info_path)
            
            # 检查必要的列是否存在
            required_columns = ['Study ID', 'Latin Name']
            if not all(col in df.columns for col in required_columns):
                logging.error(f"Excel文件缺少必要的列: {required_columns}")
                return
                
            # 创建映射：将Study ID映射到Study Name
            for _, row in df.iterrows():
                study_id = str(row['Study ID']).strip()  # 移除可能的空格
                study_name = self._clean_text(str(row['Latin Name']).strip())
                
                if pd.notna(study_id) and pd.notna(study_name) and study_name:
                    # 确保ID格式为SRID-xxx
                    if not study_id.startswith('MPMAID01-'):
                        study_id = f'MPMAID01-{study_id}'
                    self.host_mapping[study_id] = study_name
                    logging.debug(f"添加映射: {study_id} -> {study_name}")
            
            logging.info(f"成功加载了 {len(self.host_mapping)} 个宿主映射关系")
            
        except Exception as e:
            logging.error(f"加载植物信息时出错: {e}")
            logging.warning("将使用文件夹名作为植物名称")

    def _get_host_name(self, project_id: str) -> str:
        """根据项目ID获取宿主（植物）名称"""
        # 确保project_id的格式为SRID-xxx
        if not project_id.startswith('MPMAID01-'):
            project_id = f'MPMAID01-{project_id}'
        
        # 如果在映射中找不到对应的Study Name，则返回Unknown
        host_name = self.host_mapping.get(project_id, "Unknown")
        return self._clean_text(host_name)

    def process_taxonomy_file(self, file_path: Path) -> Dict[str, Dict[str, str]]:
        """处理taxonomy文件，返回OTU的分类信息"""
        logging.info(f"开始处理taxonomy文件: {file_path}")
        taxonomy_data = {}
        try:
            # 读取taxonomy文件，使用pandas处理带表头的TSV文件
            df = pd.read_csv(file_path, sep='\t')
            logging.info(f"成功读取taxonomy文件，共 {len(df)} 行")
            
            # 遍历每行
            for _, row in df.iterrows():
                otu_id = row['Feature ID']
                taxonomy_path = self._clean_text(row['Taxon'])
                confidence = row['Confidence']
                
                # 分割分类路径并去除前缀（如"d__", "p__"等）
                taxa = []
                for t in taxonomy_path.split(';'):
                    t = t.strip()
                    if not t:  # 跳过空字符串
                        continue
                    # 移除分类级别前缀
                    if '__' in t:
                        prefix, name = t.split('__', 1)
                        if name and name.lower() != 'uncultured':  # 跳过 uncultured
                            taxa.append(name)
                        else:
                            taxa.append('')  # 对于 uncultured 或空值使用空字符串
                    else:
                        taxa.append(t)
                
                # 确保有足够的分类级别
                while len(taxa) < 7:  # kingdom到species共7个级别
                    taxa.append('')
                
                taxonomy_data[otu_id] = {
                    'kingdom': taxa[0] if taxa[0] else '',
                    'phylum': taxa[1] if len(taxa) > 1 and taxa[1] else '',
                    'class': taxa[2] if len(taxa) > 2 and taxa[2] else '',
                    'order': taxa[3] if len(taxa) > 3 and taxa[3] else '',
                    'family': taxa[4] if len(taxa) > 4 and taxa[4] else '',
                    'genus': taxa[5] if len(taxa) > 5 and taxa[5] else '',
                    'taxonomy_path': taxonomy_path,
                    'confidence': confidence
                }
                
                # 如果某一级别是空的，使用上一级别的有效分类 + "_unclassified"
                last_valid = ''
                for level in ['phylum', 'class', 'order', 'family', 'genus']:
                    if not taxonomy_data[otu_id][level]:
                        if last_valid:
                            taxonomy_data[otu_id][level] = f"{last_valid}_unclassified"
                    else:
                        last_valid = taxonomy_data[otu_id][level]
                
        except Exception as e:
            logging.error(f"处理taxonomy文件时出错: {e}")
            raise
        return taxonomy_data

    def process_otu_table(self, file_path: Path, taxonomy_data: Dict[str, Dict[str, str]], project_id: str) -> Dict[str, Dict[str, Any]]:
        """处理OTU表，统计每个分类级别的信息"""
        logging.info(f"开始处理OTU表: {file_path}")
        stats = {
            'genus': {},
            'family': {},
            'order': {},
            'class': {},
            'phylum': {}
        }
        
        try:
            # 读取OTU表，跳过注释行
            df = pd.read_csv(file_path, sep='\t', skiprows=1, index_col=0)
            logging.info(f"成功读取OTU表，共 {len(df)} 行, {len(df.columns)} 列")
            
            # 如果第一列名是#OTU ID，重命名它
            if '#OTU ID' in df.index.name:
                df.index.name = 'OTU_ID'

            # 将每个样本的丰度值转换为百分比（使用向量化操作）
            df = (df / df.sum()) * 100
            
            # 获取宿主名称
            host_name = self._get_host_name(project_id)
            logging.info(f"项目 {project_id} 对应的宿主名称: {host_name}")
            
            # 预处理分类级别的数据
            level_data = {level: {} for level in stats.keys()}
            
            # 使用向量化操作处理所有OTU
            for otu_id in df.index:
                if otu_id not in taxonomy_data:
                    continue
                    
                # 获取该OTU在所有样本中的丰度
                abundances = df.loc[otu_id]
                mask = abundances > 0
                if not mask.any():
                    continue
                    
                samples_with_otu = set(df.columns[mask])
                abundance_values = abundances[mask].tolist()
                
                # 对每个分类级别进行统计
                for level in stats.keys():
                    taxon = taxonomy_data[otu_id][level]
                    if not taxon:
                        continue
                        
                    # 构建完整的分类路径
                    taxonomy_levels = ['kingdom', 'phylum', 'class', 'order', 'family', 'genus']
                    current_level_index = taxonomy_levels.index(level)
                    taxonomy_path = '; '.join([
                        f"{taxonomy_data[otu_id][l]}"
                        for l in taxonomy_levels[:current_level_index + 1]
                        if taxonomy_data[otu_id][l]
                    ])
                        
                    if taxon not in level_data[level]:
                        level_data[level][taxon] = {
                            'samples': samples_with_otu.copy(),
                            'values': abundance_values,
                            'taxonomy_path': taxonomy_path
                        }
                    else:
                        level_data[level][taxon]['samples'].update(samples_with_otu)
                        level_data[level][taxon]['values'].extend(abundance_values)
            
            # 整理每个分类级别的统计数据
            for level in stats.keys():
                for taxon, data in level_data[level].items():
                    values = data['values']
                    if not values:  # 跳过空值
                        continue
                        
                    # 计算统计值
                    mean_abundance = np.mean(values)
                    std_abundance = np.std(values) if len(values) > 1 else 0
                    
                    if taxon not in stats[level]:
                        stats[level][taxon] = {
                            'taxonomy_path': data['taxonomy_path'],
                            'nr_samples': len(data['samples']),
                            'nr_projects': 1,
                            'hosts': {host_name},
                            'abundance_data': [{
                                'host': host_name,
                                'mean': float(mean_abundance),
                                'std': float(std_abundance),
                                'min': float(np.min(values)),
                                'max': float(np.max(values)),
                                'q1': float(np.percentile(values, 25)),
                                'q3': float(np.percentile(values, 75)),
                                'median': float(np.median(values))
                            }]
                        }
                    else:
                        stats[level][taxon]['nr_samples'] += len(data['samples'])
                        stats[level][taxon]['nr_projects'] += 1
                        stats[level][taxon]['hosts'].add(host_name)
                        stats[level][taxon]['abundance_data'].append({
                            'host': host_name,
                            'mean': float(mean_abundance),
                            'std': float(std_abundance),
                            'min': float(np.min(values)),
                            'max': float(np.max(values)),
                            'q1': float(np.percentile(values, 25)),
                            'q3': float(np.percentile(values, 75)),
                            'median': float(np.median(values))
                        })
                        
        except Exception as e:
            logging.error(f"处理OTU表时出错: {e}")
            raise
            
        logging.info(f"完成OTU表处理，共处理了 {len(df.index)} 个OTU")
        return stats

    def merge_results(self, stats: Dict[str, Dict[str, Any]]):
        """合并统计结果到总结果中"""
        for level, taxa in stats.items():
            for taxon, data in taxa.items():
                key = f"{level}_{taxon}"
                if key not in self.all_results:
                    self.all_results[key] = {
                        'name': taxon,
                        'level': level,
                        'taxonomy_path': data['taxonomy_path'],
                        'nr_samples': data['nr_samples'],
                        'nr_projects': data['nr_projects'],
                        'nr_hosts': len(data['hosts']),
                        'hosts': data['hosts'],
                        'abundance_data': data['abundance_data']
                    }
                else:
                    # 更新计数
                    self.all_results[key]['nr_samples'] += data['nr_samples']
                    self.all_results[key]['nr_projects'] += data['nr_projects']
                    self.all_results[key]['hosts'].update(data['hosts'])
                    self.all_results[key]['nr_hosts'] = len(self.all_results[key]['hosts'])
                    
                    # 使用字典优化查找
                    host_data_map = {item['host']: item for item in self.all_results[key]['abundance_data']}
                    for new_data in data['abundance_data']:
                        if new_data['host'] not in host_data_map:
                            self.all_results[key]['abundance_data'].append(new_data)

    def process_all_data(self):
        """处理所有数据"""
        # 确保输出目录存在
        self.output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 获取所有项目文件夹
        project_folders = [f for f in self.base_dir.iterdir() if f.is_dir()]
        total_folders = len(project_folders)
        
        if total_folders == 0:
            logging.warning(f"警告：在 {self.base_dir} 中没有找到任何子文件夹")
            return
        
        logging.info(f"开始处理数据，共有 {total_folders} 个项目文件夹")
        
        # 添加进度显示
        for i, folder in enumerate(project_folders, 1):
            project_id = folder.name
            logging.info(f"处理文件夹 {project_id} ({i}/{total_folders}) - {(i/total_folders)*100:.1f}%")
            
            otu_file = folder / OTU_FILE_NAME
            taxonomy_file = folder / TAXONOMY_FILE_NAME
            
            if not (otu_file.exists() and taxonomy_file.exists()):
                logging.warning(f"警告: {project_id} 文件夹中缺少必要的文件")
                continue
                
            # 处理taxonomy文件
            taxonomy_data = self.process_taxonomy_file(taxonomy_file)
            if not taxonomy_data:
                continue
                
            # 处理OTU表
            stats = self.process_otu_table(otu_file, taxonomy_data, project_id)
            if not stats:
                continue
                
            # 合并结果
            self.merge_results(stats)
            
            # 定期输出内存使用情况
            if i % 10 == 0:
                logging.info(f"当前已处理 {i} 个文件夹，累计处理了 {len(self.all_results)} 个分类单元")
        
        if not self.all_results:
            logging.warning("警告：没有处理到任何有效数据")
            return
            
        logging.info("开始整理最终结果...")
        # 转换为列表格式并排序
        final_results = list(self.all_results.values())
        # 转换集合为列表
        for result in final_results:
            result['hosts'] = list(result['hosts'])
            
        final_results.sort(key=lambda x: x['nr_samples'], reverse=True)
        
        # 将numpy数据类型转换为Python原生类型
        logging.info("转换数据类型...")
        def convert_to_native_types(obj):
            if isinstance(obj, dict):
                return {key: convert_to_native_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_native_types(item) for item in obj]
            elif isinstance(obj, (np.int64, np.int32)):
                return int(obj)
            elif isinstance(obj, (np.float64, np.float32)):
                return float(obj)
            elif isinstance(obj, set):
                return list(obj)
            return obj
            
        final_results = convert_to_native_types(final_results)
        
        # 保存结果
        logging.info("保存结果到JSON文件...")
        with open(self.output_file, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, 
                     ensure_ascii=False,
                     indent=2,
                     separators=(',', ':'))
        
        logging.info(f"处理完成！统计结果已保存到 {self.output_file}")
        logging.info(f"总共处理了 {len(final_results)} 个分类单元")

if __name__ == "__main__":
    try:
        processor = MicrobesDataProcessor()
        processor.process_all_data()
    except Exception as e:
        logging.error(f"处理过程中出错: {e}")
        raise 