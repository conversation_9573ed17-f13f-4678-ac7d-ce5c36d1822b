#!/usr/bin/env python3
import os
import json
import pandas as pd
import numpy as np
import mysql.connector
from pathlib import Path
from typing import Dict, List, Any
import logging
import sys

# ============= 配置区域 =============
# 输入数据目录路径（包含多个ID子文件夹的目录）
INPUT_DIR = r"D:\Software\XAMPP\htdocs\MPRAM\data\data"

# 输出文件路径配置
OUTPUT_FILES = {
    "16S": "results/bacteria_stats.json",  # 16S数据对应细菌
    "ITS": "results/fungi_stats.json"      # ITS数据对应真菌
}

# 文件名配置
OTU_FILE_NAME = "otu_table.tsv"
TAXONOMY_FILE_NAME = "taxonomy.tsv"
PLANT_INFO_FILE = r"D:\Software\XAMPP\htdocs\MPRAM\data\information.xlsx"  # Excel文件路径，包含Study ID和Study Name

# 数据类型配置
DATA_TYPES = {
    "16S": "Bacteria",  # 16S rRNA 对应细菌数据
    "ITS": "Fungi"      # ITS 对应真菌数据
}

# ==================================

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MicrobesDataProcessor:
    def __init__(self):
        self.base_dir = Path(INPUT_DIR)
        # 为每种数据类型创建独立的结果存储
        self.results_by_type: Dict[str, Dict[str, Any]] = {
            "16S": {},  # Bacteria数据
            "ITS": {}   # Fungi数据
        }
        self.host_mapping: Dict[str, str] = {}

        # 验证输入目录是否存在
        if not self.base_dir.exists():
            raise ValueError(f"输入目录不存在: {self.base_dir}")
        if not self.base_dir.is_dir():
            raise ValueError(f"指定的路径不是目录: {self.base_dir}")

        # 加载宿主映射
        self._load_host_mapping()

    def _clean_text(self, text: str) -> str:
        """清理文本中的特殊字符"""
        if not isinstance(text, str):
            return text
        # 移除不可见字符和特殊Unicode字符
        cleaned = ''.join(char for char in text if char.isprintable())
        # 移除多余的空格
        cleaned = ' '.join(cleaned.split())
        return cleaned

    def _validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证并清理数据"""
        validated = {}
        for key, value in data.items():
            if isinstance(value, str):
                validated[key] = self._clean_text(value)
            elif isinstance(value, dict):
                validated[key] = self._validate_data(value)
            elif isinstance(value, list):
                validated[key] = [self._clean_text(x) if isinstance(x, str) else x for x in value]
            else:
                validated[key] = value
        return validated
            
    def _load_host_mapping(self):
        """从Excel文件加载ID到植物名的映射"""
        try:
            logging.info("正在从Excel文件加载植物信息...")
            plant_info_path = Path(PLANT_INFO_FILE)
            
            if not plant_info_path.exists():
                logging.warning(f"植物信息文件不存在: {plant_info_path}")
                return
                
            # 读取Excel文件
            df = pd.read_excel(plant_info_path)
            
            # 检查必要的列是否存在
            required_columns = ['Study ID', 'Latin Name']
            if not all(col in df.columns for col in required_columns):
                logging.error(f"Excel文件缺少必要的列: {required_columns}")
                return
                
            # 创建映射：将Study ID映射到Study Name
            for _, row in df.iterrows():
                study_id = str(row['Study ID']).strip()  # 移除可能的空格
                study_name = self._clean_text(str(row['Latin Name']).strip())
                
                if pd.notna(study_id) and pd.notna(study_name) and study_name:
                    # 确保ID格式为SRID-xxx
                    if not study_id.startswith('MPMAID01-'):
                        study_id = f'MPMAID01-{study_id}'
                    self.host_mapping[study_id] = study_name
                    logging.debug(f"添加映射: {study_id} -> {study_name}")
            
            logging.info(f"成功加载了 {len(self.host_mapping)} 个宿主映射关系")
            
        except Exception as e:
            logging.error(f"加载植物信息时出错: {e}")
            logging.warning("将使用文件夹名作为植物名称")

    def _get_host_name(self, data_type_id: str) -> str:
        """根据数据类型ID获取宿主（植物）名称"""
        # 从组合ID中提取项目ID（去除_16S或_ITS后缀）
        if '_16S' in data_type_id:
            project_id = data_type_id.replace('_16S', '')
        elif '_ITS' in data_type_id:
            project_id = data_type_id.replace('_ITS', '')
        else:
            project_id = data_type_id

        # 确保project_id的格式为MPMAID01-xxx
        if not project_id.startswith('MPMAID01-'):
            project_id = f'MPMAID01-{project_id}'

        # 如果在映射中找不到对应的Study Name，则返回Unknown
        host_name = self.host_mapping.get(project_id, "Unknown")
        return self._clean_text(host_name)

    def process_taxonomy_file(self, file_path: Path) -> Dict[str, Dict[str, str]]:
        """处理taxonomy文件，返回OTU的分类信息"""
        logging.info(f"开始处理taxonomy文件: {file_path}")
        taxonomy_data = {}
        try:
            # 读取taxonomy文件，使用pandas处理带表头的TSV文件
            df = pd.read_csv(file_path, sep='\t')
            logging.info(f"成功读取taxonomy文件，共 {len(df)} 行")
            
            # 遍历每行
            for _, row in df.iterrows():
                otu_id = row['Feature ID']
                taxonomy_path = self._clean_text(row['Taxon'])
                confidence = row['Confidence']
                
                # 分割分类路径并去除前缀（如"d__", "p__"等）
                taxa = []
                for t in taxonomy_path.split(';'):
                    t = t.strip()
                    if not t:  # 跳过空字符串
                        continue
                    # 移除分类级别前缀
                    if '__' in t:
                        prefix, name = t.split('__', 1)
                        if name and name.lower() != 'uncultured':  # 跳过 uncultured
                            taxa.append(name)
                        else:
                            taxa.append('')  # 对于 uncultured 或空值使用空字符串
                    else:
                        taxa.append(t)
                
                # 确保有足够的分类级别
                while len(taxa) < 7:  # kingdom到species共7个级别
                    taxa.append('')
                
                taxonomy_data[otu_id] = {
                    'kingdom': taxa[0] if taxa[0] else '',
                    'phylum': taxa[1] if len(taxa) > 1 and taxa[1] else '',
                    'class': taxa[2] if len(taxa) > 2 and taxa[2] else '',
                    'order': taxa[3] if len(taxa) > 3 and taxa[3] else '',
                    'family': taxa[4] if len(taxa) > 4 and taxa[4] else '',
                    'genus': taxa[5] if len(taxa) > 5 and taxa[5] else '',
                    'taxonomy_path': taxonomy_path,
                    'confidence': confidence
                }
                
                # 对于空值，直接保持为空，不添加_unclassified后缀
                # 这样可以避免产生过多的_unclassified分类单元
                
        except Exception as e:
            logging.error(f"处理taxonomy文件时出错: {e}")
            raise
        return taxonomy_data

    def process_otu_table(self, file_path: Path, taxonomy_data: Dict[str, Dict[str, str]], data_type_id: str) -> Dict[str, Dict[str, Any]]:
        """处理OTU表，统计每个分类级别的信息"""
        logging.info(f"开始处理OTU表: {file_path}")
        stats = {
            'genus': {},
            'family': {},
            'order': {},
            'class': {},
            'phylum': {}
        }
        
        try:
            # 读取OTU表，跳过注释行
            df = pd.read_csv(file_path, sep='\t', skiprows=1, index_col=0)
            logging.info(f"成功读取OTU表，共 {len(df)} 行, {len(df.columns)} 列")
            
            # 如果第一列名是#OTU ID，重命名它
            if '#OTU ID' in df.index.name:
                df.index.name = 'OTU_ID'

            # 将每个样本的丰度值转换为百分比（使用向量化操作）
            df = (df / df.sum()) * 100
            
            # 获取宿主名称
            host_name = self._get_host_name(data_type_id)
            logging.info(f"数据单元 {data_type_id} 对应的宿主名称: {host_name}")
            
            # 预处理分类级别的数据
            level_data = {level: {} for level in stats.keys()}
            
            # 使用向量化操作处理所有OTU
            for otu_id in df.index:
                if otu_id not in taxonomy_data:
                    continue
                    
                # 获取该OTU在所有样本中的丰度
                abundances = df.loc[otu_id]
                mask = abundances > 0
                if not mask.any():
                    continue
                    
                samples_with_otu = set(df.columns[mask])
                abundance_values = abundances[mask].tolist()
                
                # 对每个分类级别进行统计
                for level in stats.keys():
                    taxon = taxonomy_data[otu_id][level]
                    if not taxon:
                        continue
                        
                    # 构建完整的分类路径
                    taxonomy_levels = ['kingdom', 'phylum', 'class', 'order', 'family', 'genus']
                    current_level_index = taxonomy_levels.index(level)
                    taxonomy_path = '; '.join([
                        f"{taxonomy_data[otu_id][l]}"
                        for l in taxonomy_levels[:current_level_index + 1]
                        if taxonomy_data[otu_id][l]
                    ])
                        
                    if taxon not in level_data[level]:
                        level_data[level][taxon] = {
                            'samples': samples_with_otu.copy(),
                            'values': abundance_values,
                            'taxonomy_path': taxonomy_path
                        }
                    else:
                        level_data[level][taxon]['samples'].update(samples_with_otu)
                        level_data[level][taxon]['values'].extend(abundance_values)
            
            # 整理每个分类级别的统计数据
            for level in stats.keys():
                for taxon, data in level_data[level].items():
                    values = data['values']
                    if not values:  # 跳过空值
                        continue
                        
                    # 计算统计值
                    mean_abundance = np.mean(values)
                    std_abundance = np.std(values) if len(values) > 1 else 0
                    
                    if taxon not in stats[level]:
                        stats[level][taxon] = {
                            'taxonomy_path': data['taxonomy_path'],
                            'nr_samples': len(data['samples']),
                            'nr_projects': 1,
                            'hosts': {host_name},
                            'abundance_data': [{
                                'host': host_name,
                                'mean': float(mean_abundance),
                                'std': float(std_abundance),
                                'min': float(np.min(values)),
                                'max': float(np.max(values)),
                                'q1': float(np.percentile(values, 25)),
                                'q3': float(np.percentile(values, 75)),
                                'median': float(np.median(values))
                            }]
                        }
                    else:
                        stats[level][taxon]['nr_samples'] += len(data['samples'])
                        stats[level][taxon]['nr_projects'] += 1
                        stats[level][taxon]['hosts'].add(host_name)
                        stats[level][taxon]['abundance_data'].append({
                            'host': host_name,
                            'mean': float(mean_abundance),
                            'std': float(std_abundance),
                            'min': float(np.min(values)),
                            'max': float(np.max(values)),
                            'q1': float(np.percentile(values, 25)),
                            'q3': float(np.percentile(values, 75)),
                            'median': float(np.median(values))
                        })
                        
        except Exception as e:
            logging.error(f"处理OTU表时出错: {e}")
            raise
            
        logging.info(f"完成OTU表处理，共处理了 {len(df.index)} 个OTU")
        return stats

    def merge_results(self, stats: Dict[str, Dict[str, Any]], data_type: str):
        """合并统计结果到对应数据类型的结果中"""
        target_results = self.results_by_type[data_type]

        for level, taxa in stats.items():
            for taxon, data in taxa.items():
                key = f"{level}_{taxon}"
                if key not in target_results:
                    target_results[key] = {
                        'name': taxon,
                        'level': level,
                        'taxonomy_path': data['taxonomy_path'],
                        'nr_samples': data['nr_samples'],
                        'nr_projects': data['nr_projects'],
                        'nr_hosts': len(data['hosts']),
                        'hosts': data['hosts'],
                        'abundance_data': data['abundance_data']
                    }
                else:
                    # 更新计数
                    target_results[key]['nr_samples'] += data['nr_samples']
                    target_results[key]['nr_projects'] += data['nr_projects']
                    target_results[key]['hosts'].update(data['hosts'])
                    target_results[key]['nr_hosts'] = len(target_results[key]['hosts'])

                    # 使用字典优化查找
                    host_data_map = {item['host']: item for item in target_results[key]['abundance_data']}
                    for new_data in data['abundance_data']:
                        if new_data['host'] not in host_data_map:
                            target_results[key]['abundance_data'].append(new_data)

    def process_all_data(self):
        """处理所有数据"""
        # 确保输出目录存在
        for output_file in OUTPUT_FILES.values():
            Path(output_file).parent.mkdir(parents=True, exist_ok=True)

        # 获取所有项目文件夹
        project_folders = [f for f in self.base_dir.iterdir() if f.is_dir()]
        total_folders = len(project_folders)

        if total_folders == 0:
            logging.warning(f"警告：在 {self.base_dir} 中没有找到任何子文件夹")
            return

        logging.info(f"开始处理数据，共有 {total_folders} 个项目文件夹")

        # 计算总的处理单元数（项目数 × 子文件夹数）
        total_processing_units = 0
        for folder in project_folders:
            for data_type in DATA_TYPES.keys():
                subfolder = folder / data_type
                if subfolder.exists() and subfolder.is_dir():
                    otu_file = subfolder / OTU_FILE_NAME
                    taxonomy_file = subfolder / TAXONOMY_FILE_NAME
                    if otu_file.exists() and taxonomy_file.exists():
                        total_processing_units += 1

        logging.info(f"总共找到 {total_processing_units} 个有效的数据单元需要处理")

        # 添加进度显示
        processed_count = 0
        for i, folder in enumerate(project_folders, 1):
            project_id = folder.name
            logging.info(f"处理项目文件夹 {project_id} ({i}/{total_folders})")

            # 处理每个数据类型（16S 和 ITS）
            for data_type, organism_type in DATA_TYPES.items():
                subfolder = folder / data_type
                if not (subfolder.exists() and subfolder.is_dir()):
                    logging.debug(f"跳过: {project_id} 中没有找到 {data_type} 子文件夹")
                    continue

                otu_file = subfolder / OTU_FILE_NAME
                taxonomy_file = subfolder / TAXONOMY_FILE_NAME

                if not (otu_file.exists() and taxonomy_file.exists()):
                    logging.warning(f"警告: {project_id}/{data_type} 文件夹中缺少必要的文件")
                    continue

                processed_count += 1
                data_type_id = f"{project_id}_{data_type}"
                logging.info(f"处理 {organism_type} 数据: {data_type_id} ({processed_count}/{total_processing_units}) - {(processed_count/total_processing_units)*100:.1f}%")

                # 处理taxonomy文件
                taxonomy_data = self.process_taxonomy_file(taxonomy_file)
                if not taxonomy_data:
                    continue

                # 处理OTU表，使用组合ID作为项目标识
                stats = self.process_otu_table(otu_file, taxonomy_data, data_type_id)
                if not stats:
                    continue

                # 合并结果到对应的数据类型
                self.merge_results(stats, data_type)

                # 定期输出内存使用情况
                if processed_count % 10 == 0:
                    bacteria_count = len(self.results_by_type["16S"])
                    fungi_count = len(self.results_by_type["ITS"])
                    logging.info(f"当前已处理 {processed_count} 个数据单元，Bacteria: {bacteria_count} 个分类单元，Fungi: {fungi_count} 个分类单元")

        # 检查是否有数据需要处理
        has_data = any(len(results) > 0 for results in self.results_by_type.values())
        if not has_data:
            logging.warning("警告：没有处理到任何有效数据")
            return

        logging.info("开始整理最终结果...")

        # 将numpy数据类型转换为Python原生类型的函数
        def convert_to_native_types(obj):
            if isinstance(obj, dict):
                return {key: convert_to_native_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_native_types(item) for item in obj]
            elif isinstance(obj, (np.int64, np.int32)):
                return int(obj)
            elif isinstance(obj, (np.float64, np.float32)):
                return float(obj)
            elif isinstance(obj, set):
                return list(obj)
            return obj

        # 分别处理和保存每种数据类型的结果
        for data_type, organism_type in DATA_TYPES.items():
            results = self.results_by_type[data_type]

            if not results:
                logging.info(f"跳过 {organism_type} ({data_type})：没有找到相关数据")
                continue

            logging.info(f"处理 {organism_type} ({data_type}) 数据...")

            # 转换为列表格式并排序
            final_results = list(results.values())

            # 转换集合为列表
            for result in final_results:
                result['hosts'] = list(result['hosts'])

            final_results.sort(key=lambda x: x['nr_samples'], reverse=True)

            # 转换数据类型
            final_results = convert_to_native_types(final_results)

            # 保存结果到对应的文件
            output_file = Path(OUTPUT_FILES[data_type])
            logging.info(f"保存 {organism_type} 结果到 {output_file}...")

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(final_results, f,
                         ensure_ascii=False,
                         indent=2,
                         separators=(',', ':'))

            logging.info(f"{organism_type} 数据处理完成！共 {len(final_results)} 个分类单元")

        logging.info("=" * 60)
        logging.info("所有数据处理完成！")
        bacteria_count = len(self.results_by_type["16S"])
        fungi_count = len(self.results_by_type["ITS"])
        logging.info(f"Bacteria (16S): {bacteria_count} 个分类单元 -> {OUTPUT_FILES['16S']}")
        logging.info(f"Fungi (ITS): {fungi_count} 个分类单元 -> {OUTPUT_FILES['ITS']}")
        logging.info("=" * 60)

if __name__ == "__main__":
    try:
        processor = MicrobesDataProcessor()
        processor.process_all_data()
    except Exception as e:
        logging.error(f"处理过程中出错: {e}")
        raise 