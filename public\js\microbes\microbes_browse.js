/**
 * Microbes Browse Page JavaScript
 * Handles microbe browsing, filtering, and visualization
 */

class MicrobesBrowser {
    constructor() {
        this.currentPage = 1;
        this.itemsPerPage = 20; // 增加表格每页显示数量
        this.totalItems = 0;
        this.currentData = [];
        this.viewMode = 'table'; // 默认为表格视图

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadInitialData();
    }
    
    bindEvents() {
        // Search and filter events
        $('#searchBtn').on('click', () => this.performSearch());
        $('#searchInput').on('keypress', (e) => {
            if (e.which === 13) this.performSearch();
        });
        
        // Filter change events
        $('#levelSelect, #groupSelect, #partSelect, #sortSelect, #orderSelect').on('change', () => {
            this.performSearch();
        });

        // View mode toggle
        $('#viewModeTable').on('click', () => this.setViewMode('table'));
        $('#viewModeGrid').on('click', () => this.setViewMode('grid'));
        
        // Pagination events will be bound dynamically
    }
    
    async loadInitialData() {
        try {
            this.showLoading();

            // 添加超时处理
            const timeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('请求超时')), 10000)
            );

            await Promise.race([
                Promise.all([
                    this.loadStatistics(),
                    this.performSearch()
                ]),
                timeout
            ]);
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('数据加载失败，请检查网络连接或稍后重试。');
        }
    }
    
    async loadStatistics() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch('../../api/microbes/get_statistics_demo.php', {
                signal: controller.signal
            });
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                $('#totalMicrobes').text(this.formatNumber(data.statistics.total_microbes));
                $('#totalHosts').text(this.formatNumber(data.statistics.total_hosts));
                $('#totalSamples').text(this.formatNumber(data.statistics.total_samples));
                $('#totalProjects').text(this.formatNumber(data.statistics.total_projects));
            } else {
                throw new Error(data.message || '统计数据加载失败');
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
            // 显示默认值
            $('#totalMicrobes').text('--');
            $('#totalHosts').text('--');
            $('#totalSamples').text('--');
            $('#totalProjects').text('--');
        }
    }
    
    async performSearch() {
        try {
            this.showLoading();

            const params = {
                level: $('#levelSelect').val(),
                group: $('#groupSelect').val(),
                part: $('#partSelect').val(),
                search: $('#searchInput').val(),
                sort: $('#sortSelect').val(),
                order: $('#orderSelect').val(),
                page: this.currentPage,
                per_page: this.itemsPerPage
            };

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000);

            const response = await fetch('../../api/microbes/get_microbes_list_demo.php?' + new URLSearchParams(params), {
                signal: controller.signal
            });
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.currentData = data.microbes || [];
                this.totalItems = data.total || 0;
                this.renderResults();
                this.renderPagination();
            } else {
                this.showError(data.message || '微生物数据加载失败');
            }
        } catch (error) {
            console.error('Error performing search:', error);
            if (error.name === 'AbortError') {
                this.showError('请求超时，请检查网络连接');
            } else {
                this.showError('搜索失败，请稍后重试');
            }
        } finally {
            this.hideLoading();
        }
    }
    
    renderResults() {
        if (this.currentData.length === 0) {
            this.showNoResults();
            return;
        }

        $('#resultsCount').text(`Found ${this.formatNumber(this.totalItems)} microbes`);
        $('#resultsContainer').show();
        $('#noResults').hide();

        if (this.viewMode === 'table') {
            this.renderTableView();
        } else {
            this.renderGridView();
        }
    }

    renderTableView() {
        const tbody = $('#microbesTableBody');
        tbody.empty();

        this.currentData.forEach(microbe => {
            const row = this.createTableRow(microbe);
            tbody.append(row);
        });
    }

    renderGridView() {
        const container = $('#gridView');
        container.empty();

        this.currentData.forEach(microbe => {
            const card = this.createMicrobeCard(microbe);
            container.append(card);
        });
    }
    
    createTableRow(microbe) {
        // 解析taxonomy路径
        const taxonomy = this.parseTaxonomy(microbe.taxonomy_path);

        // 获取部位信息（从part_abundance_data中提取）
        const parts = this.extractParts(microbe.part_abundance_data);
        const partsText = parts.length > 0 ? parts.join(', ') : 'N/A';
        const shortParts = partsText.length > 20 ? partsText.substring(0, 20) + '...' : partsText;

        return $(`
            <tr>
                <td class="microbe-name-cell" title="${microbe.name}">${microbe.name}</td>
                <td class="taxonomy-cell" title="${taxonomy.kingdom}">${taxonomy.kingdom}</td>
                <td class="taxonomy-cell" title="${taxonomy.phylum}">${taxonomy.phylum}</td>
                <td class="taxonomy-cell" title="${taxonomy.class}">${taxonomy.class}</td>
                <td class="taxonomy-cell" title="${taxonomy.order}">${taxonomy.order}</td>
                <td class="taxonomy-cell" title="${taxonomy.family}">${taxonomy.family}</td>
                <td class="taxonomy-cell" title="${taxonomy.genus}">${taxonomy.genus}</td>
                <td class="taxonomy-cell" title="${taxonomy.species}">${taxonomy.species}</td>
                <td class="parts-cell" title="${partsText}">${shortParts}</td>
                <td class="stat-cell">${this.formatNumber(microbe.nr_hosts)}</td>
                <td class="stat-cell">${this.formatNumber(microbe.nr_samples)}</td>
                <td class="stat-cell">${this.formatNumber(microbe.nr_projects)}</td>
                <td class="text-center">
                    <a href="microbe_detail.php?id=${microbe.id}&name=${encodeURIComponent(microbe.name)}&level=${microbe.level}&group=${$('#groupSelect').val()}"
                       class="btn-view-details-sm">
                        <i class="fas fa-eye"></i>
                    </a>
                </td>
            </tr>
        `);
    }

    createMicrobeCard(microbe) {
        const taxonomyPath = microbe.taxonomy_path || 'Unknown taxonomy';
        const shortTaxonomy = taxonomyPath.length > 80 ?
            taxonomyPath.substring(0, 80) + '...' : taxonomyPath;

        return $(`
            <div class="col-md-6 col-lg-4">
                <div class="microbe-card">
                    <div class="microbe-card-header">
                        <div class="microbe-name">${microbe.name}</div>
                        <div class="taxonomy-path" title="${taxonomyPath}">${shortTaxonomy}</div>
                    </div>
                    <div class="microbe-card-body">
                        <div class="microbe-stats">
                            <div class="stat-item">
                                <div class="stat-number">${this.formatNumber(microbe.nr_hosts)}</div>
                                <div class="stat-label">Hosts</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${this.formatNumber(microbe.nr_samples)}</div>
                                <div class="stat-label">Samples</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${this.formatNumber(microbe.nr_projects)}</div>
                                <div class="stat-label">Projects</div>
                            </div>
                        </div>

                        <div class="chart-container" id="chart-${microbe.id}"></div>

                        <div class="text-center mt-3">
                            <a href="microbe_detail.php?id=${microbe.id}&name=${encodeURIComponent(microbe.name)}&level=${microbe.level}&group=${$('#groupSelect').val()}"
                               class="btn-view-details">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `);
    }

    parseTaxonomy(taxonomyPath) {
        const taxonomy = {
            kingdom: '',
            phylum: '',
            class: '',
            order: '',
            family: '',
            genus: '',
            species: ''
        };

        if (!taxonomyPath) return taxonomy;

        const parts = taxonomyPath.split(';').map(part => part.trim());
        const levels = ['kingdom', 'phylum', 'class', 'order', 'family', 'genus', 'species'];

        parts.forEach((part, index) => {
            if (index < levels.length && part) {
                taxonomy[levels[index]] = part;
            }
        });

        return taxonomy;
    }

    extractParts(partAbundanceData) {
        const parts = [];

        if (!partAbundanceData) return parts;

        try {
            const data = typeof partAbundanceData === 'string' ?
                JSON.parse(partAbundanceData) : partAbundanceData;

            if (Array.isArray(data)) {
                data.forEach(item => {
                    if (item.part && !parts.includes(item.part)) {
                        parts.push(item.part);
                    }
                });
            } else if (typeof data === 'object') {
                Object.keys(data).forEach(key => {
                    if (key.includes('_') && !parts.includes(key.split('_')[1])) {
                        parts.push(key.split('_')[1]);
                    }
                });
            }
        } catch (e) {
            console.warn('Error parsing part abundance data:', e);
        }

        return parts;
    }
    
    renderPagination() {
        const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        const pagination = $('#pagination');
        pagination.empty();
        
        if (totalPages <= 1) return;
        
        // Previous button
        const prevDisabled = this.currentPage === 1 ? 'disabled' : '';
        pagination.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" data-page="${this.currentPage - 1}">Previous</a>
            </li>
        `);
        
        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            pagination.append(`<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`);
            if (startPage > 2) {
                pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const active = i === this.currentPage ? 'active' : '';
            pagination.append(`
                <li class="page-item ${active}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `);
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
            pagination.append(`<li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>`);
        }
        
        // Next button
        const nextDisabled = this.currentPage === totalPages ? 'disabled' : '';
        pagination.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" data-page="${this.currentPage + 1}">Next</a>
            </li>
        `);
        
        // Bind pagination events
        pagination.find('a.page-link').on('click', (e) => {
            e.preventDefault();
            const page = parseInt($(e.target).data('page'));
            if (page && page !== this.currentPage) {
                this.currentPage = page;
                this.performSearch();
            }
        });
    }
    
    setViewMode(mode) {
        this.viewMode = mode;
        $('#viewModeTable, #viewModeGrid').removeClass('active');
        $(`#viewMode${mode.charAt(0).toUpperCase() + mode.slice(1)}`).addClass('active');

        // Show/hide appropriate views
        if (mode === 'table') {
            $('#tableView').show();
            $('#gridView').hide();
            this.itemsPerPage = 20; // 表格模式显示更多项目
        } else {
            $('#tableView').hide();
            $('#gridView').show();
            this.itemsPerPage = 12; // 网格模式显示较少项目
        }

        this.renderResults();
    }
    
    showLoading() {
        $('#loadingSpinner').show();
        $('#resultsContainer, #noResults').hide();
    }
    
    hideLoading() {
        $('#loadingSpinner').hide();
    }
    
    showNoResults() {
        $('#noResults').show();
        $('#resultsContainer').hide();
    }
    
    showError(message) {
        console.error(message);
        $('#resultsSection').html(`
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `);
    }
    
    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return parseInt(num).toLocaleString();
    }
}

// Initialize when document is ready
$(document).ready(() => {
    window.microbesBrowser = new MicrobesBrowser();
});

// Export for global access
window.MicrobesBrowser = MicrobesBrowser;
