<?php
/**
 * 一键设置优化数据库
 * 创建表结构、导入数据、性能测试
 */

set_time_limit(0);
ini_set('memory_limit', '2G');

require_once '../includes/config/db_connection.php';
require_once 'import_optimized_data.php';

class DatabaseSetup {
    private $conn;
    
    public function __construct($db_connection) {
        $this->conn = $db_connection;
    }
    
    private function log($message) {
        echo "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
        flush();
    }
    
    public function setup() {
        $this->log("=== 开始设置优化数据库 ===");
        
        try {
            // 1. 创建表结构
            $this->createTables();
            
            // 2. 导入数据
            $this->importData();
            
            // 3. 性能测试
            $this->performanceTest();
            
            // 4. 生成报告
            $this->generateReport();
            
            $this->log("=== 设置完成 ===");
            
        } catch (Exception $e) {
            $this->log("错误: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function createTables() {
        $this->log("创建数据库表结构...");
        
        $schema_file = '../database/optimized_microbes_schema.sql';
        if (!file_exists($schema_file)) {
            throw new Exception("找不到数据库结构文件: {$schema_file}");
        }
        
        $sql = file_get_contents($schema_file);
        
        // 分割SQL语句
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^(--|\/\*)/', $stmt);
            }
        );
        
        foreach ($statements as $statement) {
            if (stripos($statement, 'DELIMITER') !== false) {
                continue; // 跳过DELIMITER语句
            }
            
            try {
                $this->conn->query($statement);
                $this->log("执行SQL: " . substr($statement, 0, 50) . "...");
            } catch (Exception $e) {
                $this->log("SQL执行警告: " . $e->getMessage());
                // 继续执行，某些语句可能因为表已存在而失败
            }
        }
        
        $this->log("数据库表结构创建完成");
    }
    
    private function importData() {
        $this->log("开始导入数据...");
        
        $importer = new OptimizedDataImporter($this->conn);
        
        // 设置进度回调
        $importer->setProgressCallback(function($message) {
            $this->log($message);
        });
        
        // 检查数据文件
        $bacteria_file = '../results/optimized/bacteria_optimized.json';
        $fungi_file = '../results/optimized/fungi_optimized.json';
        
        if (!file_exists($bacteria_file)) {
            throw new Exception("找不到细菌数据文件: {$bacteria_file}");
        }
        
        if (!file_exists($fungi_file)) {
            throw new Exception("找不到真菌数据文件: {$fungi_file}");
        }
        
        // 导入细菌数据
        $this->log("导入细菌数据...");
        $start_time = microtime(true);
        $importer->importData('bacteria');
        $bacteria_time = microtime(true) - $start_time;
        $this->log("细菌数据导入完成，耗时: " . round($bacteria_time, 2) . " 秒");
        
        // 导入真菌数据
        $this->log("导入真菌数据...");
        $start_time = microtime(true);
        $importer->importData('fungi');
        $fungi_time = microtime(true) - $start_time;
        $this->log("真菌数据导入完成，耗时: " . round($fungi_time, 2) . " 秒");
        
        // 创建索引
        $this->log("创建搜索索引...");
        $importer->createIndexes();
        
        // 优化数据库
        $this->log("优化数据库...");
        $importer->optimizeDatabase();
        
        $total_time = $bacteria_time + $fungi_time;
        $this->log("数据导入总耗时: " . round($total_time, 2) . " 秒");
    }
    
    private function performanceTest() {
        $this->log("开始性能测试...");
        
        $tests = [
            '基本查询' => "SELECT COUNT(*) FROM microbes_metadata",
            '复杂查询' => "SELECT m.*, COUNT(h.id) as host_count FROM microbes_metadata m LEFT JOIN microbes_host_part_stats h ON m.id = h.microbe_id GROUP BY m.id LIMIT 10",
            '搜索查询' => "SELECT * FROM microbes_full_info WHERE name LIKE '%Pseudomonas%' LIMIT 10",
            '统计查询' => "SELECT data_type, level, COUNT(*) FROM microbes_metadata GROUP BY data_type, level",
            '宿主查询' => "SELECT DISTINCT host_name FROM microbes_host_part_stats LIMIT 20"
        ];
        
        $results = [];
        
        foreach ($tests as $test_name => $sql) {
            $start_time = microtime(true);
            
            try {
                $result = $this->conn->query($sql);
                $row_count = $result->num_rows;
                $end_time = microtime(true);
                
                $execution_time = ($end_time - $start_time) * 1000; // 转换为毫秒
                $results[$test_name] = [
                    'success' => true,
                    'time_ms' => round($execution_time, 2),
                    'rows' => $row_count
                ];
                
                $this->log("{$test_name}: {$execution_time}ms ({$row_count} 行)");
                
            } catch (Exception $e) {
                $results[$test_name] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                $this->log("{$test_name}: 失败 - " . $e->getMessage());
            }
        }
        
        // 测试API性能
        $this->log("测试API性能...");
        $api_tests = [
            '统计API' => 'http://localhost/MPRAM/api/microbes/get_microbes_optimized.php?action=stats',
            '列表API' => 'http://localhost/MPRAM/api/microbes/get_microbes_optimized.php?action=list&per_page=20',
            '宿主API' => 'http://localhost/MPRAM/api/microbes/get_microbes_optimized.php?action=hosts'
        ];
        
        foreach ($api_tests as $test_name => $url) {
            $start_time = microtime(true);
            
            try {
                $response = file_get_contents($url);
                $end_time = microtime(true);
                
                $execution_time = ($end_time - $start_time) * 1000;
                $data = json_decode($response, true);
                
                $results[$test_name] = [
                    'success' => $data['success'] ?? false,
                    'time_ms' => round($execution_time, 2),
                    'size_bytes' => strlen($response)
                ];
                
                $this->log("{$test_name}: {$execution_time}ms (" . strlen($response) . " bytes)");
                
            } catch (Exception $e) {
                $results[$test_name] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                $this->log("{$test_name}: 失败 - " . $e->getMessage());
            }
        }
        
        return $results;
    }
    
    private function generateReport() {
        $this->log("生成性能报告...");
        
        // 获取数据库统计
        $stats = [];
        
        // 表大小
        $table_sizes = $this->conn->query("
            SELECT 
                table_name,
                table_rows,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
            AND table_name LIKE 'microbes_%'
            ORDER BY (data_length + index_length) DESC
        ")->fetch_all(MYSQLI_ASSOC);
        
        // 数据统计
        $data_stats = $this->conn->query("
            SELECT 
                'bacteria' as type,
                COUNT(*) as count
            FROM microbes_metadata WHERE data_type = 'bacteria'
            UNION ALL
            SELECT 
                'fungi' as type,
                COUNT(*) as count
            FROM microbes_metadata WHERE data_type = 'fungi'
        ")->fetch_all(MYSQLI_ASSOC);
        
        $report = [
            'setup_time' => date('Y-m-d H:i:s'),
            'table_sizes' => $table_sizes,
            'data_statistics' => $data_stats,
            'recommendations' => [
                '数据库已优化，支持高性能查询',
                '建议定期清理过期缓存',
                '监控查询性能，必要时添加索引',
                '考虑使用Redis等外部缓存系统'
            ]
        ];
        
        // 保存报告
        $report_file = '../reports/database_setup_report_' . date('Y-m-d_H-i-s') . '.json';
        @mkdir(dirname($report_file), 0755, true);
        file_put_contents($report_file, json_encode($report, JSON_PRETTY_PRINT));
        
        $this->log("性能报告已保存: {$report_file}");
        
        // 显示摘要
        $this->log("=== 设置摘要 ===");
        foreach ($table_sizes as $table) {
            $this->log("表 {$table['table_name']}: {$table['table_rows']} 行, {$table['size_mb']} MB");
        }
        
        foreach ($data_stats as $stat) {
            $this->log("{$stat['type']}: {$stat['count']} 个分类单元");
        }
    }
}

// 执行设置
if (php_sapi_name() === 'cli') {
    try {
        $setup = new DatabaseSetup($conn);
        $setup->setup();
        echo "\n数据库设置成功完成！\n";
    } catch (Exception $e) {
        echo "\n设置失败: " . $e->getMessage() . "\n";
        exit(1);
    }
} else {
    echo "请在命令行中运行此脚本\n";
}
?>
