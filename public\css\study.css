/* Study Page Specific Styles */

/* Basic layout styles */
body {
    padding-top: 70px;
}

/* Content wrapper styles */
.content-wrapper {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

/* Content section styles */
.content-section {
    margin-bottom: 20px;
    padding: 0;
}

.content-section:last-child {
    margin-bottom: 0;
}

/* Title styles */
.content-section h4 {
    color: #002060;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Table container styles */
.table-responsive {
    margin: 0;
    border-radius: 8px;
    overflow-x: auto;
    overflow-y: visible;
    border: 1px solid #dee2e6;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    -webkit-overflow-scrolling: touch;
}

/* Ensure tables can scroll horizontally on small screens */
.table-responsive table {
    min-width: 600px;
    white-space: nowrap;
}

/* Table cell styles */
.table-responsive td,
.table-responsive th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

/* For link cells, allow appropriate width */
.table-responsive td:has(.herb-detail-link),
.table-responsive td:first-child {
    min-width: 120px;
    max-width: 250px;
}

/* Improve table scrollbar styles */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Table hover effect */
.table-responsive .table-hover tbody tr:hover {
    background-color: rgba(0, 32, 96, 0.05);
}

/* Ensure table header stays visible during horizontal scroll */
.table-responsive thead th {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10;
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
}

/* Table styles */
.table {
    margin-bottom: 0;
    width: 100%;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    color: #495057;
    font-weight: 600;
    padding: 12px;
    white-space: nowrap;
}

.table td {
    padding: 12px;
    vertical-align: middle;
}

/* Taxa Composition section styles */
.charts-container {
    margin-top: 20px;
}

.level-selector {
    margin-bottom: 25px;
}

.data-type-switch {
    margin-bottom: 20px;
}

/* Button group styles */
.btn-group {
    margin-bottom: 15px;
}

.btn-group .btn {
    padding: 8px 16px;
}

/* Chart container styles */
#pieChart, #stackChart {
    border-radius: 8px;
    background-color: transparent;
}

/* Pie chart container styles */
.pie-chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: transparent;
}

#pieChart {
    width: 100%;
    box-shadow: none;
    background-color: transparent;
}

/* Table related style optimization */
.taxa-table-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

#taxaTable thead.sticky-top {
    z-index: 990;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

/* Slider container styles */
.sample-range {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
}

/* Mobile-first responsive design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 0 20px;
    }

    /* Adjust left control panel */
    .col-lg-2.col-md-3 {
        max-width: 100px;
    }

    .display-mode-panel, .part-type-panel, .data-type-panel, .level-selector-panel {
        padding: 10px;
        margin-bottom: 10px;
    }
}

@media (max-width: 992px) {
    /* Stack layout for medium screens */
    .row .col-lg-2.col-md-3 {
        max-width: 100%;
        margin-bottom: 20px;
    }

    .row .col-lg-10.col-md-9 {
        max-width: 100%;
    }

    /* Horizontal layout for control panels */
    .display-mode-options {
        flex-direction: row !important;
        justify-content: space-between;
        gap: 8px;
    }

    .mode-option {
        flex: 1;
        text-align: center;
        padding: 8px 4px;
    }

    .mode-text {
        font-size: 12px;
    }

    .btn-group-vertical {
        flex-direction: row !important;
        gap: 5px;
    }

    .btn-group-vertical .btn {
        flex: 1;
        margin-bottom: 0;
        font-size: 12px;
        padding: 6px 8px;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 60px; /* Reduce top padding for mobile */
    }

    .container-fluid {
        padding: 0 15px;
    }

    .content-wrapper {
        padding: 15px;
        margin: 10px 0;
    }

    .content-section {
        margin-bottom: 25px;
    }

    /* Mobile sidebar */
    #sidebar {
        position: static;
        margin-bottom: 20px;
        padding: 10px;
        max-width: 100%;
    }



    #sidebar .list-group-item {
        padding: 10px 15px;
        font-size: 16px;
        text-align: center;
        margin-bottom: 5px;
    }

    /* Mobile main content */
    .col-md-9 {
        max-width: 100%;
        flex: 1;
    }

    /* Mobile table improvements */
    .table-responsive {
        margin-bottom: 25px !important;
        border-radius: 6px;
        max-height: 300px !important;
    }

    .table-responsive table {
        min-width: 500px;
        font-size: 13px;
    }

    .table-responsive td,
    .table-responsive th {
        padding: 8px 6px;
        white-space: nowrap;
    }

    /* Mobile charts */
    .col-md-6.d-flex {
        margin-bottom: 25px;
    }

    #pieChart {
        height: 350px !important;
    }

    #stackChart {
        height: 300px !important;
        margin-top: 40px;
    }

    /* Mobile control panels - compact layout */
    .display-mode-panel, .part-type-panel, .data-type-panel, .level-selector-panel {
        padding: 8px;
        margin-bottom: 8px;
        font-size: 13px;
    }

    .display-mode-panel h6, .part-type-panel h6, .data-type-panel h6, .level-selector-panel h6 {
        font-size: 13px !important;
        margin-bottom: 6px;
    }

    /* Mobile sample range controls */
    .sample-range {
        padding: 10px 15px;
        margin-top: 15px;
    }

    .sample-id-main-title {
        margin: -25px 0 15px;
        font-size: 14px;
    }

    .range-title {
        font-size: 12px;
    }

    .range-labels {
        font-size: 11px;
    }

    /* Mobile download buttons */
    .btn-group .btn {
        font-size: 12px;
        padding: 6px 10px;
    }

    /* Mobile pagination */
    .pagination .page-link {
        padding: 4px 8px;
        font-size: 12px;
    }

    /* Mobile table info */
    #tableInfo {
        font-size: 12px;
    }

    .d-flex.justify-content-between.align-items-center {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .d-flex.justify-content-between.align-items-center nav {
        align-self: center;
    }
}

@media (max-width: 576px) {
    /* Extra small screens */
    body {
        padding-top: 50px;
    }

    .content-wrapper {
        padding: 10px;
        margin: 5px 0;
    }

    .content-section h4 {
        font-size: 1.1rem;
    }

    /* Very compact control panels */
    .display-mode-options {
        flex-direction: column;
        gap: 4px;
    }

    .mode-option {
        padding: 6px 8px;
        font-size: 12px;
    }

    .mode-text {
        font-size: 11px;
    }

    .btn-group-vertical {
        flex-direction: column !important;
    }

    .btn-group-vertical .btn {
        font-size: 11px;
        padding: 5px 8px;
        margin-bottom: 3px;
    }

    /* Extra small charts */
    #pieChart {
        height: 280px !important;
    }

    #stackChart {
        height: 250px !important;
    }

    /* Extra small tables */
    .table-responsive {
        max-height: 250px !important;
        font-size: 11px;
    }

    .table-responsive table {
        min-width: 400px;
    }

    .table-responsive td,
    .table-responsive th {
        padding: 6px 4px;
        max-width: 120px;
    }

    /* Back to top button adjustments */
    .back-to-top-card {
        margin: 10px 3px 3px 3px;
        padding: 8px;
    }

    .sidebar-back-to-top-btn {
        padding: 6px 12px;
        font-size: 10px;
        border-radius: 15px;
    }

    .sidebar-back-to-top-btn i {
        font-size: 10px;
    }

    .sidebar-back-to-top-btn span {
        font-size: 9px;
    }
}

/* Mobile-specific enhancements */
@media (max-width: 768px) {
    /* Mobile sidebar toggle */
    .mobile-sidebar-toggle {
        display: block;
        position: fixed;
        top: 70px;
        left: 10px;
        z-index: 1000;
        background: #002060;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
    }

    .mobile-sidebar-toggle:hover {
        background: #003080;
        transform: scale(1.1);
    }

    .mobile-sidebar-toggle.hidden {
        transform: translateX(-60px);
        opacity: 0;
    }

    /* Mobile sidebar overlay */
    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1040;
    }

    .sidebar-overlay.show {
        display: block;
    }

    /* Mobile sidebar slide-in */
    #sidebar.mobile-sidebar {
        position: fixed;
        top: 60px;
        left: -320px;
        width: 300px;
        height: calc(100vh - 60px);
        background: white;
        z-index: 1050;
        transition: left 0.3s ease;
        overflow-y: auto;
        box-shadow: 2px 0 10px rgba(0,0,0,0.3);
    }

    #sidebar.mobile-sidebar.show {
        left: 0;
    }

    /* Mobile table horizontal scroll indicator */
    .table-scroll-indicator {
        display: block;
        text-align: center;
        font-size: 12px;
        color: #666;
        margin-bottom: 10px;
        padding: 5px;
        background: #f8f9fa;
        border-radius: 4px;
        animation: pulse 2s infinite;
    }

    .table-scroll-indicator::before {
        content: "👈 ";
    }

    .table-scroll-indicator::after {
        content: " 👉";
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    /* Enhanced mobile table styles */
    .table-responsive {
        position: relative;
        overflow-x: auto;
        overflow-y: visible;
        -webkit-overflow-scrolling: touch;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .table-responsive::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 20px;
        height: 100%;
        background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
        pointer-events: none;
        z-index: 1;
    }

    /* Mobile chart containers */
    .mobile-chart-wrapper {
        position: relative;
        margin: 15px 0;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .mobile-chart-title {
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
        padding: 5px;
        background: white;
        border-radius: 4px;
    }

    /* Mobile control panels enhancement */
    .mobile-controls-container {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
    }

    .mobile-controls-title {
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        color: #002060;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }
}

@media (min-width: 769px) {
    .mobile-sidebar-toggle {
        display: none;
    }

    .table-scroll-indicator {
        display: none;
    }
}

/* Enhanced mobile table styles */
@media (max-width: 576px) {
    /* Compact table for very small screens */
    .table-responsive.mobile-compact {
        font-size: 10px;
    }

    .table-responsive.mobile-compact table {
        min-width: 350px;
    }

    .table-responsive.mobile-compact td,
    .table-responsive.mobile-compact th {
        padding: 4px 3px;
        max-width: 100px;
        font-size: 10px;
    }

    /* Mobile-specific table headers */
    .mobile-table-header {
        display: block;
        background: #f8f9fa;
        padding: 8px;
        margin-bottom: 10px;
        border-radius: 4px;
        font-size: 12px;
        text-align: center;
        font-weight: 600;
    }

    /* Mobile chart container improvements */
    .mobile-chart-container {
        position: relative;
        margin: 10px 0;
    }

    .mobile-chart-container::before {
        content: "📊 ";
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 16px;
        background: white;
        padding: 0 10px;
        z-index: 10;
    }

    /* Mobile download buttons stack */
    .mobile-download-stack .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .mobile-download-stack .btn {
        margin-bottom: 5px;
        width: 100%;
    }
}

/* Print styles */
@media print {
    body {
        padding-top: 0;
    }

    header, .mobile-sidebar-toggle, .sidebar-overlay, .back-to-top-card {
        display: none !important;
    }

    #sidebar {
        display: none !important;
    }

    .content-wrapper {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .table-responsive {
        overflow: visible !important;
        max-height: none !important;
    }

    #pieChart, #stackChart {
        break-inside: avoid;
    }
}

@media (max-width: 576px) {
    #pieChart {
        height: 350px !important;
    }

    .table-responsive.flex-grow-1 {
        max-height: 300px !important;
    }

    /* Mobile table data labels */
    .table-responsive table tbody tr td::before {
        content: attr(data-label) ": ";
        font-weight: 600;
        color: #495057;
        display: none;
    }

    /* Mobile card-style table for very small screens */
    @media (max-width: 480px) {
        .table-responsive.mobile-card-view table,
        .table-responsive.mobile-card-view thead,
        .table-responsive.mobile-card-view tbody,
        .table-responsive.mobile-card-view th,
        .table-responsive.mobile-card-view td,
        .table-responsive.mobile-card-view tr {
            display: block;
        }

        .table-responsive.mobile-card-view thead tr {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }

        .table-responsive.mobile-card-view tr {
            border: 1px solid #ccc;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-responsive.mobile-card-view td {
            border: none;
            padding: 5px 0;
            text-align: left;
        }

        .table-responsive.mobile-card-view td::before {
            display: inline-block;
            width: 40%;
            margin-right: 10px;
        }
    }

    /* Mobile scroll indicator */
    .mobile-scroll-indicator {
        position: absolute !important;
        bottom: 5px !important;
        right: 10px !important;
        background: rgba(0,32,96,0.8) !important;
        color: white !important;
        padding: 2px 6px !important;
        border-radius: 10px !important;
        font-size: 10px !important;
        z-index: 10 !important;
        pointer-events: none !important;
    }

    /* Mobile chart touch improvements */
    .mobile-chart-container {
        touch-action: pan-x pan-y;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    /* Mobile button improvements */
    .btn {
        min-height: 44px; /* iOS recommended touch target size */
        padding: 8px 16px;
    }

    .btn-sm {
        min-height: 36px;
        padding: 6px 12px;
    }

    /* Mobile form controls */
    .form-select, .form-control {
        min-height: 44px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Mobile pagination improvements */
    .pagination .page-link {
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* Adjust overall layout */
.container-fluid {
    padding: 0 30px;
}

.content-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Sidebar styles */
#sidebar {
    position: sticky;
    top: 85px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px 0;
}

/* Table style optimization */
.table-responsive {
    margin-bottom: 30px !important;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    color: #495057;
    font-weight: 600;
    padding: 12px;
}

.table td {
    padding: 12px;
    vertical-align: middle;
}

/* Pagination style optimization */
.fixed-table-pagination {
    padding: 5px;
    background-color: #fff;
    border-top: 1px solid #dee2e6;
    margin-bottom: 0 !important;
}

/* Responsive layout optimization */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 15px;
    }

    #sidebar {
        position: static;
        margin-bottom: 20px;
        padding: 10px;
    }

    #sidebar .list-group-item {
        padding: 8px 12px;
        font-size: 14px;
        text-align: center;
    }

    .content-container {
        padding: 15px;
    }

    /* Ensure tables can scroll horizontally on small screens */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
    }

    .table-responsive table {
        min-width: 500px !important;
    }
}

/* Remove old navigation styles, use common.css */

/* Remove top blank area */
body::before {
    display: none !important;
    content: none !important;
    height: 0 !important;
}

/* Adjust content area top margin */
.content-section {
    margin-top: 0;
    padding-top: 1rem;
}

/* Ensure navigation bar is fixed at top */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}

/* 1. Collapsible title, abstract */
.text-truncate-container {
    position: relative;
    max-width: 100%;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.expanded {
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
}

.text-truncate-container {
    display: flex;
    align-items: center;
}

.text-truncate {
    flex: 1;
    overflow: hidden;
}

.btn-link {
    text-decoration: none;
    color: #002060;
    font-weight: bold;
    margin-left: 10px;
}

.btn-link:hover {
    text-decoration: underline;
    color: #002060;
}

/* 2. Modify table styles */
table.table-borderless {
    border-collapse: collapse;
    width: 100%;
}

table.table-borderless th,
table.table-borderless td {
    border: none;
    padding: 8px;
}

/* Set alternating row background colors */
table.table-borderless tr:nth-child(odd) {
    background-color: #f9f9f9;
}

table.table-borderless tr:nth-child(even) {
    background-color: #ffffff;
}

/* Table header fixed white background */
table.table-borderless thead th {
    background-color: #ffffff;
    position: sticky;
    top: 0;
    z-index: 1;
}

/* 3. Fixed navigation bar and beautification styles */
#sidebar .list-group-item {
    color: #666;
    background-color: transparent;
    border: none;
    padding: 16px 20px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    line-height: 1.4;
    text-align: left;
    font-size: 19px;
}

/* Hover effect */
#sidebar .list-group-item:hover {
    background-color: #e9ecef;
    color: #333;
    text-decoration: none;
}

/* Current active navigation item style */
#sidebar .list-group-item.active {
    background-color: #e3f2fd;
    color: #002060;
    font-weight: bold;
    border-left: 3px solid #002060;
}

#sidebar .list-group-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: #002060;
    border-radius: 2px;
}

/* Adjust scrollbar styles */
#sidebar::-webkit-scrollbar {
    width: 5px;
}

#sidebar::-webkit-scrollbar-thumb {
    background-color: #002060;
    border-radius: 10px;
}

#sidebar::-webkit-scrollbar-track {
    background-color: #343a40;
}

/* Sidebar back to top button card styles */
.back-to-top-card {
    margin: 15px 8px 8px 8px;
    padding: 15px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
    border: 2px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.back-to-top-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #002060, #003080, #0040a0);
}

.sidebar-back-to-top-btn {
    background: linear-gradient(145deg, #002060, #003080);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 10px 18px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 700;
    box-shadow: 0 4px 15px rgba(0, 32, 96, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.sidebar-back-to-top-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
}

.sidebar-back-to-top-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 32, 96, 0.4);
    background: linear-gradient(145deg, #003080, #0040a0);
}

.sidebar-back-to-top-btn:hover::before {
    left: 100%;
}

.sidebar-back-to-top-btn:active {
    transform: translateY(-1px) scale(0.98);
    box-shadow: 0 4px 15px rgba(0, 32, 96, 0.3);
}

.sidebar-back-to-top-btn i {
    font-size: 14px;
    animation: bounce 2s infinite;
}

.sidebar-back-to-top-btn span {
    font-size: 11px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-2px);
    }
}

/* Modify button styles */
.btn-group .btn-outline-primary {
    border-color: #ddd;
    color: #666;
    background-color: #fff;
    margin-right: 2px;
}

.btn-group .btn-outline-primary:hover {
    background-color: #f8f9fa;
    border-color: #666;
    color: #333;
}

.btn-group .btn-outline-primary.active {
    background-color: #002060 !important;
    border-color: #002060 !important;
    color: #fff !important;
    font-weight: bold;
}

/* Ensure button group styles are correct */
.level-selector .btn-group {
    display: inline-flex;
    gap: 5px;
}

.level-selector .btn {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

/* Slider container styles */
.range-slider {
    width: 100%;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
}

/* Slider track styles */
.range-container {
    position: relative;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    margin: 10px 0 8px;
}

/* Slider styles */
.form-range {
    -webkit-appearance: none;
    position: absolute;
    top: -8px;
    width: 100%;
    height: 20px;
    background: transparent;
    pointer-events: none;
    z-index: 5;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    pointer-events: auto;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #007bff;
    cursor: pointer;
    margin-top: -6px;
    z-index: 10;
    position: relative;
}

/* Slider value display */
.range-values {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    color: #666;
    font-size: 14px;
}

.range-label {
    color: #333;
    font-weight: 500;
    margin-bottom: 10px;
}

/* Table styles */
#taxaTable {
    border-collapse: collapse;
    width: 100%;
}

#taxaTable th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    color: #333;
}

#taxaTable td {
    padding: 10px 12px;
    border-bottom: 1px solid #dee2e6;
}

#taxaTable tbody tr:hover {
    background-color: #f8f9fa;
}

/* Pagination styles */
.pagination {
    margin: 0;
}

.pagination .page-link {
    padding: 6px 12px;
    color: #666;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #002060;
    border-color: #002060;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #ccc;
}

/* Modify chart and table spacing */
.chart-section {
    margin-bottom: 40px;
}

/* Adjust table top spacing */
.table-section {
    margin-top: 40px;
}

/* Sample ID main title styles */
.sample-id-main-title {
    text-align: center;
    color: #333;
    font-size: 16px;
    font-weight: 500;
    margin: -45px 0 25px;
}

/* Range selection title styles */
.range-title {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

/* Sample range control styles */
.sample-range {
    width: 100%;
    padding: 5px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 10px;
    position: relative;
}

.range-container {
    position: relative;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    margin: 10px 0 8px;
    z-index: 1;
}

.form-range.sample-slider {
    position: absolute;
    top: -8px;
    width: 100%;
    height: 20px;
    background: transparent;
    pointer-events: none;
    z-index: 5;
}

.form-range.sample-slider::-webkit-slider-thumb {
    pointer-events: auto;
    margin-top: -6px;
    position: relative;
    z-index: 10;
}

.range-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
    margin-bottom: 2px;
}

/* Modify styles */
#stackChart {
    width: 100%;
    height: 400px;
    margin-top: 60px;
    min-height: 300px;
}

/* Ensure slider track doesn't interfere with slider dragging */
.range-track {
    pointer-events: none;
}

.selected-range {
    pointer-events: none;
}

/* Responsive layout optimization */
@media (max-width: 1200px) {
    .chart-section {
        margin-bottom: 40px;
    }

    #pieChart, #stackChart {
        height: 350px;
    }

    .grid {
        left: '5%';
        right: '5%';
        bottom: '15%';
    }
}

@media (max-width: 768px) {
    #pieChart, #stackChart {
        height: 300px;
    }

    .sample-id-main-title {
        margin: -35px 0 20px;
    }

    .range-container {
        margin: 8px 0 6px;
    }
}

/* If you need to override spacing in specific cases, you can add more specific selectors */
@media (max-width: 768px) {
    body .content-container .text-start:nth-child(3),
    body .content-container .text-start:nth-child(4) {
        margin-top: 60px !important;
    }

    /* Fix mobile layout issues */
    .content-section {
        margin-bottom: 20px !important;
        padding: 10px !important;
    }

    .content-section h4 {
        font-size: 18px !important;
        margin-bottom: 15px !important;
    }

    /* Fix table responsive on mobile */
    .table-responsive {
        margin-bottom: 20px !important;
        font-size: 12px !important;
    }

    /* Fix alert/tips area on mobile */
    .alert {
        margin: 15px 0 !important;
        padding: 10px !important;
        font-size: 12px !important;
    }

    .alert strong {
        font-size: 14px !important;
    }
}

/* Styles migrated from style.css */
@media (min-width: 768px){
    .news-input{
        width:70%;
    }
}

/* Content container styles */
.content-container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Left control panel optimization styles */
.display-mode-panel, .part-type-panel, .data-type-panel, .level-selector-panel {
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    font-size: 15px;
    margin-bottom: 12px !important;
}

.display-mode-panel h6, .part-type-panel h6, .data-type-panel h6, .level-selector-panel h6 {
    font-size: 15px !important;
    color: #495057;
    margin-bottom: 10px !important;
    text-align: center;
    font-weight: 600;
}

.mode-option {
    padding: 8px 10px;
    margin: 3px 0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 15px;
    text-align: center;
    background-color: white;
    border: 1px solid #dee2e6;
}

.mode-option:hover {
    background-color: #e9ecef;
}

.mode-option.active {
    background-color: #002060;
    color: white;
    border-color: #002060;
}

.mode-icon {
    font-size: 14px;
    margin-bottom: 2px;
}

.mode-text {
    font-size: 14px;
    font-weight: 500;
}

.btn-group-vertical .btn {
    font-size: 15px !important;
    padding: 6px 10px !important;
    margin: 2px 0;
    border-radius: 5px !important;
}

.part-types-container .part-option {
    padding: 6px 8px;
    margin: 2px 0;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    background-color: white;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.part-option:hover {
    background-color: #e9ecef;
}

.part-option.active {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

/* Study.php page styles */
.text-start {
    margin: 40px 0;
    padding-top: 15px;
    width: 100%;
}

/* First text-start doesn't need top margin */
.text-start:first-child {
    margin-top: 30px;
    padding-top: 0;
}

/* Last text-start doesn't need bottom margin */
.text-start:last-child {
    margin-bottom: 30px;
}

/* Unified title styles */
.text-start h4 {
    font-size: 1.2rem;
    color: #002060;
    margin-bottom: 1rem;
    padding: 0;
    font-weight: 700;
}

/* Unified divider styles */
.text-start hr {
    margin: 1rem 0;
    opacity: 0.8;
    width: 100%;
}

/* Ensure content area width consistency */
.text-dark {
    width: 100%;
    padding: 0;
}

/* Table section style adjustments */
.table-container,
.table-responsive {
    width: 100%;
    margin: 0;
    padding: 0;
}

.table-container {
    overflow-x: auto;
}

/* Remove excessive bottom spacing from tables */
.table-responsive {
    margin-bottom: 30px !important;
}

/* Last table doesn't need bottom spacing */
.table-responsive:last-child {
    margin-bottom: 0 !important;
}

/* Chart related styles */
.chart-section {
    margin-bottom: 60px;
}

.pie-chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 400px;
}

#stackChart {
    width: 100%;
    height: 400px;
    margin-top: 60px;
    min-height: 300px;
}

/* Responsive layout */
@media (max-width: 1200px) {
    .chart-section {
        margin-bottom: 40px;
    }

    #pieChart, #stackChart {
        height: 350px;
    }
}

@media (max-width: 768px) {
    #pieChart, #stackChart {
        height: 300px;
    }

    .sample-id-main-title {
        margin: -35px 0 20px;
    }

    .table-responsive {
        margin-bottom: 30px !important;
    }
}

/* Add top margin for titles after bootstrap-table */
div[class*="table"] + .text-start,
.fixed-table-pagination + .text-start,
[data-toggle="table"] + .text-start {
    margin-top: 40px;
}

/* Force top margin for Soil Physicochemical properties title */
#title-3,
.text-start h4#title-3 {
    margin-top: 50px !important;
}

/* Hide description text */
.hidden-text {
    color: white !important;
}

/* Fix table height, avoid excessive whitespace */
.bootstrap-table .fixed-table-container {
    height: auto !important;
}

/* Override bootstrap-table internal styles */
.bootstrap-table .fixed-table-container .fixed-table-body {
    height: auto !important;
    overflow: visible !important;
}

/* Table responsive layout styles */
.taxa-table-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.table-responsive {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

#taxaTable thead.sticky-top {
    z-index: 990;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

/* Left control panel styles */
.display-mode-panel, .part-type-panel, .data-type-panel, .level-selector-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #dee2e6;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08);
}

.display-mode-panel h6, .part-type-panel h6, .data-type-panel h6, .level-selector-panel h6 {
    color: #495057;
    font-size: 0.75rem;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 700;
}

/* Display mode option styles */
.display-mode-options {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.mode-option {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-radius: 6px;
    background: white;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.mode-option:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.15);
    transform: translateY(-1px);
}

.mode-option.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-color: #0056b3;
    box-shadow: 0 2px 6px rgba(0,123,255,0.3);
}

.mode-icon {
    font-size: 1rem;
    margin-right: 6px;
    min-width: 18px;
}

.mode-text {
    font-weight: 500;
    font-size: 0.8rem;
}

/* Part type container styles */
.part-types-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.part-type-option {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    background: white;
    border: 1px solid #dee2e6;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.part-type-option:hover {
    border-color: #28a745;
    background: #f8fff8;
}

.part-type-option.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-color: #20c997;
}

.part-type-option .part-icon {
    margin-right: 6px;
    font-size: 0.9rem;
}

/* Vertical button group style optimization */
.btn-group-vertical .btn {
    border-radius: 4px !important;
    margin-bottom: 4px;
    font-size: 0.75rem;
    padding: 6px 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

.btn-group-vertical .btn:hover {
    transform: translateX(1px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-6.d-flex {
        margin-bottom: 30px;
    }

    #pieChart {
        height: 400px !important;
    }

    .table-responsive {
        max-height: 350px !important;
    }

    /* Mobile left panel adjustments */
    .col-md-2 {
        margin-bottom: 15px;
    }

    .display-mode-panel, .part-type-panel, .data-type-panel, .level-selector-panel {
        padding: 8px;
        margin-bottom: 10px;
    }

    .display-mode-panel h6, .part-type-panel h6, .data-type-panel h6, .level-selector-panel h6 {
        font-size: 0.7rem;
        margin-bottom: 6px;
    }

    .display-mode-options {
        flex-direction: row;
        justify-content: space-between;
        gap: 4px;
    }

    .mode-option {
        flex: 1;
        margin: 0 1px;
        padding: 6px 4px;
        text-align: center;
    }

    .mode-text {
        font-size: 0.7rem;
    }

    .mode-icon {
        font-size: 0.9rem;
        margin-right: 2px;
    }

    .btn-group-vertical .btn {
        font-size: 0.7rem;
        padding: 4px 6px;
        margin-bottom: 3px;
    }
}

@media (max-width: 576px) {
    #pieChart {
        height: 350px !important;
    }

    .display-mode-options {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 6px;
    }

    .mode-option {
        padding: 5px 6px;
    }

    .mode-text {
        font-size: 0.65rem;
    }

    .table-responsive {
        max-height: 300px !important;
        overflow-x: auto !important;
        overflow-y: auto !important;
    }

    .table-responsive table {
        min-width: 450px !important;
        font-size: 12px;
    }

    .table-responsive td,
    .table-responsive th {
        padding: 6px 8px;
        max-width: 150px;
    }

    #sidebar .list-group-item {
        padding: 8px 14px;
        font-size: 16px;
        margin-bottom: 4px;
    }

    .back-to-top-card {
        margin: 12px 5px 5px 5px;
        padding: 12px;
    }

    .sidebar-back-to-top-btn {
        padding: 8px 15px;
        font-size: 11px;
        border-radius: 20px;
    }

    .sidebar-back-to-top-btn i {
        font-size: 12px;
    }

    .sidebar-back-to-top-btn span {
        font-size: 10px;
    }
}

/* Responsive adjustments to stack charts and improve controls on tablets/phones */
@media (max-width: 992px) {
  .charts-container .row > .col-md-6 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }
  .data-type-panel .btn-group-vertical {
    display: flex;
    flex-direction: row;
    gap: 6px;
  }
  .data-type-panel .btn-group-vertical .btn {
    flex: 1;
    width: auto;
  }
}

/* Mobile/Tablet: make taxa controls a horizontal toolbar above charts */
@media (max-width: 992px){
  .taxa-controls-column{
    flex: 0 0 100% !important;
    max-width: 100% !important;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 10px;
    position: sticky; /* keep visible while scrolling */
    top: 70px;
    z-index: 100;
  }
  /* Remove bottom margins inside toolbar to keep compact */
  .taxa-controls-column > .mb-3{ margin-bottom: 0 !important; }

  /* Each panel behaves like a small card in toolbar */
  .taxa-controls-column .display-mode-panel,
  .taxa-controls-column .part-type-panel,
  .taxa-controls-column .data-type-panel,
  .taxa-controls-column .level-selector-panel{
    flex: 1 1 calc(50% - 8px);
    min-width: 260px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 8px 10px !important;
  }

  /* Buttons horizontally aligned */
  .taxa-controls-column .btn-group-vertical{
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap;
    gap: 6px;
  }
  .taxa-controls-column .btn-group-vertical .btn{ width: auto; flex: 0 0 auto; }

  /* Display-mode options as pills */
  .taxa-controls-column .display-mode-options{ display:flex; flex-wrap:wrap; gap:8px; }
  .taxa-controls-column .mode-option{
    display:flex; align-items:center; gap:6px; padding:6px 10px; border:1px solid #dee2e6; border-radius:9999px; cursor:pointer; user-select:none;
    background:#fff; color:#0d6efd;
  }
  .taxa-controls-column .mode-option.active{ background:#e7f1ff; border-color:#0d6efd; }
  .taxa-controls-column .mode-icon{ line-height:1; }
  .taxa-controls-column .mode-text{ font-size: .85rem; }

  /* Part types container as wrap */
  .taxa-controls-column .part-types-container{ display:flex; flex-wrap:wrap; gap:6px; }
}




/* Make the left controls sticky on mobile and place at top horizontally */
@media (max-width: 768px){
  .taxa-controls-column{ position: sticky; top: 60px; z-index: 100; }
  .mobile-controls-container{ display: flex; flex-direction: row; flex-wrap: wrap; gap: 8px; align-items: stretch; }
  .mobile-controls-container > .display-mode-panel,
  .mobile-controls-container > .part-type-panel,
  .mobile-controls-container > .data-type-panel,
  .mobile-controls-container > .level-selector-panel{ flex: 1 1 calc(50% - 8px); min-width: 42%; }
  .mobile-chart-wrapper{ margin-top: 10px; }
}

/* Keep NCBI/ENA buttons inline with label */
.accession-links{ display: inline-flex; align-items: center; gap: 6px; flex-wrap: wrap; }

/* Improve placement of floating mobile sidebar toggle to not hide under header */
@media (max-width: 768px){
  .mobile-sidebar-toggle{ top: 90px; z-index: 1100; }
}

/* Mobile reflow for charts and sliders */
@media (max-width: 992px){
  .charts-container .row > .col-md-6{ flex:0 0 100%; max-width:100%; }
  #stackChart{ margin-top: 20px !important; }
  .sample-range .range-container{ display:flex; gap:8px; align-items:center; }
}

/* Swiper mobile card layout */
.mobile-study-swiper{ width:100%; height:auto; }
.mobile-study-swiper .swiper-slide{ padding-top:8px; }
@media (max-width: 992px){
  /* 隐藏桌面布局，显示滑动布局通过 .d-lg-none/.d-lg-block 控制 */
  #pieChartMobile, #stackChartMobile{ width:100%; }
}
