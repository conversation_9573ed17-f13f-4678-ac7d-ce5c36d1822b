<?php
/**
 * 优化的微生物数据API
 * 高性能查询，支持分页、缓存、过滤
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../includes/config/db_connection.php';

class OptimizedMicrobesAPI {
    private $conn;
    private $cache_enabled = true;
    private $cache_ttl = 3600; // 1小时缓存
    
    public function __construct($db_connection) {
        $this->conn = $db_connection;
    }
    
    public function handleRequest() {
        try {
            $action = $_GET['action'] ?? $_POST['action'] ?? 'list';
            
            switch ($action) {
                case 'list':
                    return $this->getMicrobesList();
                case 'detail':
                    return $this->getMicrobeDetail();
                case 'search':
                    return $this->searchMicrobes();
                case 'stats':
                    return $this->getStatistics();
                case 'hosts':
                    return $this->getHosts();
                case 'analysis':
                    return $this->getAnalysisData();
                default:
                    throw new Exception('未知的操作类型');
            }
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
    
    private function getMicrobesList() {
        // 获取参数
        $level = $_GET['level'] ?? '';
        $data_type = $_GET['data_type'] ?? '';
        $search = $_GET['search'] ?? '';
        $host_filter = $_GET['host_filter'] ?? '';
        $page = max(1, intval($_GET['page'] ?? 1));
        $per_page = min(100, max(10, intval($_GET['per_page'] ?? 20)));
        $sort = $_GET['sort'] ?? 'name';
        $order = strtoupper($_GET['order'] ?? 'ASC');
        
        // 验证排序参数
        $allowed_sorts = ['name', 'nr_samples', 'nr_hosts', 'avg_mean_abundance'];
        if (!in_array($sort, $allowed_sorts)) {
            $sort = 'name';
        }
        if (!in_array($order, ['ASC', 'DESC'])) {
            $order = 'ASC';
        }
        
        $offset = ($page - 1) * $per_page;
        
        // 构建缓存键
        $cache_key = "microbes_list_" . md5(serialize([
            $level, $data_type, $search, $host_filter, $page, $per_page, $sort, $order
        ]));
        
        // 尝试从缓存获取
        if ($this->cache_enabled) {
            $cached = $this->getFromCache($cache_key);
            if ($cached) {
                return $cached;
            }
        }
        
        // 构建查询
        $where_conditions = ['1=1'];
        $params = [];
        $param_types = '';
        
        if (!empty($level)) {
            $where_conditions[] = 'm.level = ?';
            $params[] = $level;
            $param_types .= 's';
        }
        
        if (!empty($data_type)) {
            $where_conditions[] = 'm.data_type = ?';
            $params[] = $data_type;
            $param_types .= 's';
        }
        
        if (!empty($search)) {
            $where_conditions[] = '(m.name LIKE ? OR m.taxonomy_path LIKE ?)';
            $search_term = "%{$search}%";
            $params[] = $search_term;
            $params[] = $search_term;
            $param_types .= 'ss';
        }
        
        if (!empty($host_filter)) {
            $where_conditions[] = 'm.id IN (SELECT DISTINCT microbe_id FROM microbes_host_part_stats WHERE host_name LIKE ?)';
            $params[] = "%{$host_filter}%";
            $param_types .= 's';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM microbes_full_info m WHERE {$where_clause}";
        $count_stmt = $this->conn->prepare($count_sql);
        if (!empty($params)) {
            $count_stmt->bind_param($param_types, ...$params);
        }
        $count_stmt->execute();
        $total = $count_stmt->get_result()->fetch_assoc()['total'];
        
        // 获取数据
        $data_sql = "
            SELECT 
                m.id, m.name, m.level, m.data_type, m.taxonomy_path,
                m.nr_samples, m.nr_hosts, m.nr_parts, m.nr_projects,
                m.actual_hosts, m.actual_parts, m.avg_mean_abundance, m.max_max_abundance
            FROM microbes_full_info m 
            WHERE {$where_clause}
            ORDER BY m.{$sort} {$order}
            LIMIT ? OFFSET ?
        ";
        
        $data_stmt = $this->conn->prepare($data_sql);
        $params[] = $per_page;
        $params[] = $offset;
        $param_types .= 'ii';
        
        if (!empty($params)) {
            $data_stmt->bind_param($param_types, ...$params);
        }
        $data_stmt->execute();
        $result = $data_stmt->get_result();
        
        $microbes = [];
        while ($row = $result->fetch_assoc()) {
            $microbes[] = $row;
        }
        
        $response = [
            'success' => true,
            'data' => $microbes,
            'pagination' => [
                'page' => $page,
                'per_page' => $per_page,
                'total' => intval($total),
                'total_pages' => ceil($total / $per_page)
            ],
            'filters' => [
                'level' => $level,
                'data_type' => $data_type,
                'search' => $search,
                'host_filter' => $host_filter
            ]
        ];
        
        // 缓存结果
        if ($this->cache_enabled) {
            $this->saveToCache($cache_key, $response);
        }
        
        return $response;
    }
    
    private function getMicrobeDetail() {
        $id = intval($_GET['id'] ?? 0);
        $host_filter = $_GET['host_filter'] ?? '';
        $part_filter = $_GET['part_filter'] ?? '';
        
        if ($id <= 0) {
            throw new Exception('无效的微生物ID');
        }
        
        // 缓存键
        $cache_key = "microbe_detail_{$id}_" . md5($host_filter . $part_filter);
        
        if ($this->cache_enabled) {
            $cached = $this->getFromCache($cache_key);
            if ($cached) {
                return $cached;
            }
        }
        
        // 获取基本信息
        $basic_stmt = $this->conn->prepare("SELECT * FROM microbes_metadata WHERE id = ?");
        $basic_stmt->bind_param('i', $id);
        $basic_stmt->execute();
        $basic_info = $basic_stmt->get_result()->fetch_assoc();
        
        if (!$basic_info) {
            throw new Exception('微生物不存在');
        }
        
        // 获取宿主-部位统计
        $stats_sql = "
            SELECT * FROM microbes_host_part_stats 
            WHERE microbe_id = ?
        ";
        $params = [$id];
        $param_types = 'i';
        
        if (!empty($host_filter)) {
            $stats_sql .= " AND host_name LIKE ?";
            $params[] = "%{$host_filter}%";
            $param_types .= 's';
        }
        
        if (!empty($part_filter)) {
            $stats_sql .= " AND part_name LIKE ?";
            $params[] = "%{$part_filter}%";
            $param_types .= 's';
        }
        
        $stats_sql .= " ORDER BY host_name, part_name";
        
        $stats_stmt = $this->conn->prepare($stats_sql);
        $stats_stmt->bind_param($param_types, ...$params);
        $stats_stmt->execute();
        $stats_result = $stats_stmt->get_result();
        
        $host_part_stats = [];
        while ($row = $stats_result->fetch_assoc()) {
            $host_part_stats[$row['host_part_key']] = [
                'host_name' => $row['host_name'],
                'part_name' => $row['part_name'],
                'mean' => floatval($row['mean_abundance']),
                'std' => floatval($row['std_abundance']),
                'sem' => floatval($row['sem_abundance']),
                'median' => floatval($row['median_abundance']),
                'min' => floatval($row['min_abundance']),
                'max' => floatval($row['max_abundance']),
                'q1' => floatval($row['q1_abundance']),
                'q3' => floatval($row['q3_abundance']),
                'count' => intval($row['sample_count']),
                'ci_lower' => floatval($row['ci_lower']),
                'ci_upper' => floatval($row['ci_upper'])
            ];
        }
        
        $response = [
            'success' => true,
            'data' => array_merge($basic_info, [
                'host_part_stats' => $host_part_stats
            ])
        ];
        
        if ($this->cache_enabled) {
            $this->saveToCache($cache_key, $response);
        }
        
        return $response;
    }
    
    private function getStatistics() {
        $cache_key = 'microbes_statistics';
        
        if ($this->cache_enabled) {
            $cached = $this->getFromCache($cache_key);
            if ($cached) {
                return $cached;
            }
        }
        
        // 获取统计数据
        $stats = [];
        
        // 总体统计
        $total_stmt = $this->conn->query("
            SELECT 
                COUNT(*) as total_microbes,
                COUNT(DISTINCT CASE WHEN data_type = 'bacteria' THEN id END) as total_bacteria,
                COUNT(DISTINCT CASE WHEN data_type = 'fungi' THEN id END) as total_fungi
            FROM microbes_metadata
        ");
        $total_stats = $total_stmt->fetch_assoc();
        
        // 宿主统计
        $host_stmt = $this->conn->query("
            SELECT COUNT(DISTINCT host_name) as total_hosts
            FROM microbes_host_part_stats
        ");
        $host_stats = $host_stmt->fetch_assoc();
        
        // 样本统计
        $sample_stmt = $this->conn->query("
            SELECT SUM(nr_samples) as total_samples
            FROM microbes_metadata
        ");
        $sample_stats = $sample_stmt->fetch_assoc();
        
        $response = [
            'success' => true,
            'statistics' => array_merge($total_stats, $host_stats, $sample_stats)
        ];
        
        if ($this->cache_enabled) {
            $this->saveToCache($cache_key, $response);
        }
        
        return $response;
    }
    
    private function getHosts() {
        $cache_key = 'microbes_hosts';
        
        if ($this->cache_enabled) {
            $cached = $this->getFromCache($cache_key);
            if ($cached) {
                return $cached;
            }
        }
        
        $stmt = $this->conn->query("
            SELECT DISTINCT host_name as host, COUNT(*) as microbe_count
            FROM microbes_host_part_stats
            GROUP BY host_name
            ORDER BY host_name
        ");
        
        $hosts = [];
        while ($row = $stmt->fetch_assoc()) {
            $hosts[] = $row;
        }
        
        $response = [
            'success' => true,
            'hosts' => $hosts
        ];
        
        if ($this->cache_enabled) {
            $this->saveToCache($cache_key, $response);
        }
        
        return $response;
    }
    
    private function getFromCache($key) {
        $stmt = $this->conn->prepare("
            SELECT cache_data FROM microbes_cache 
            WHERE cache_key = ? AND expires_at > NOW()
        ");
        $stmt->bind_param('s', $key);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        
        return $result ? json_decode($result['cache_data'], true) : null;
    }
    
    private function saveToCache($key, $data) {
        $cache_data = json_encode($data);
        $expires_at = date('Y-m-d H:i:s', time() + $this->cache_ttl);
        
        $stmt = $this->conn->prepare("
            INSERT INTO microbes_cache (cache_key, cache_data, cache_type, expires_at)
            VALUES (?, ?, 'api', ?)
            ON DUPLICATE KEY UPDATE
            cache_data = VALUES(cache_data),
            expires_at = VALUES(expires_at)
        ");
        $stmt->bind_param('sss', $key, $cache_data, $expires_at);
        $stmt->execute();
    }
    
    private function errorResponse($message) {
        return [
            'success' => false,
            'error' => $message
        ];
    }
}

// 处理请求
try {
    $api = new OptimizedMicrobesAPI($conn);
    $response = $api->handleRequest();
    echo json_encode($response);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
