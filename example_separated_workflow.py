#!/usr/bin/env python3
"""
演示分离处理Bacteria和Fungi数据的工作流程
"""

def show_directory_structure():
    """展示目录结构"""
    print("=== 数据目录结构 ===")
    structure = """
    data/data/
    ├── MPMAID01-001/          # 完整项目（细菌+真菌）
    │   ├── 16S/               # 细菌数据 (Bacteria)
    │   │   ├── otu_table.tsv
    │   │   └── taxonomy.tsv
    │   └── ITS/               # 真菌数据 (Fungi)
    │       ├── otu_table.tsv
    │       └── taxonomy.tsv
    ├── MPMAID01-002/          # 仅细菌项目
    │   └── 16S/               # 只有细菌数据
    │       ├── otu_table.tsv
    │       └── taxonomy.tsv
    ├── MPMAID01-003/          # 仅真菌项目
    │   └── ITS/               # 只有真菌数据
    │       ├── otu_table.tsv
    │       └── taxonomy.tsv
    └── MPMAID01-004/          # 完整项目
        ├── 16S/
        │   ├── otu_table.tsv
        │   └── taxonomy.tsv
        └── ITS/
            ├── otu_table.tsv
            └── taxonomy.tsv
    """
    print(structure)

def show_processing_workflow():
    """展示处理工作流程"""
    print("=== 处理工作流程 ===")
    
    steps = [
        {
            "step": 1,
            "action": "扫描项目文件夹",
            "description": "遍历data/data/下的所有MPMAID01-xxx文件夹"
        },
        {
            "step": 2, 
            "action": "检测数据类型",
            "description": "在每个项目下查找16S和ITS子文件夹"
        },
        {
            "step": 3,
            "action": "分类处理16S数据",
            "description": "处理细菌数据，存储到Bacteria结果集"
        },
        {
            "step": 4,
            "action": "分类处理ITS数据", 
            "description": "处理真菌数据，存储到Fungi结果集"
        },
        {
            "step": 5,
            "action": "生成分离的输出",
            "description": "分别输出bacteria_stats.json和fungi_stats.json"
        }
    ]
    
    for step_info in steps:
        print(f"{step_info['step']}. {step_info['action']}")
        print(f"   {step_info['description']}")

def show_output_comparison():
    """展示输出对比"""
    print("\n=== 输出文件对比 ===")
    
    print("【旧版本】单一输出:")
    print("  microbes_stats.json")
    print("  ├── 混合了细菌和真菌数据")
    print("  ├── 难以区分数据来源")
    print("  └── 分析时需要手动筛选")
    
    print("\n【新版本】分离输出:")
    print("  bacteria_stats.json (16S数据)")
    print("  ├── 仅包含细菌分类统计")
    print("  ├── 清晰的细菌群落结构")
    print("  └── 便于细菌特异性分析")
    print()
    print("  fungi_stats.json (ITS数据)")
    print("  ├── 仅包含真菌分类统计")
    print("  ├── 清晰的真菌群落结构")
    print("  └── 便于真菌特异性分析")

def show_data_examples():
    """展示数据示例"""
    print("\n=== 数据内容示例 ===")
    
    bacteria_example = {
        "name": "Escherichia",
        "level": "genus",
        "taxonomy_path": "Bacteria; Proteobacteria; Gammaproteobacteria; Enterobacteriales; Enterobacteriaceae; Escherichia",
        "nr_samples": 45,
        "nr_projects": 12,
        "nr_hosts": 8,
        "hosts": ["Arabidopsis thaliana", "Oryza sativa", "Zea mays"],
        "abundance_data": [
            {
                "host": "Arabidopsis thaliana",
                "mean": 2.34,
                "std": 1.12,
                "median": 2.1
            }
        ]
    }
    
    fungi_example = {
        "name": "Aspergillus", 
        "level": "genus",
        "taxonomy_path": "Fungi; Ascomycota; Eurotiomycetes; Eurotiales; Aspergillaceae; Aspergillus",
        "nr_samples": 32,
        "nr_projects": 8,
        "nr_hosts": 5,
        "hosts": ["Triticum aestivum", "Solanum lycopersicum"],
        "abundance_data": [
            {
                "host": "Triticum aestivum",
                "mean": 1.87,
                "std": 0.95,
                "median": 1.6
            }
        ]
    }
    
    print("bacteria_stats.json 示例条目:")
    print("  分类单元: Escherichia (细菌属)")
    print("  分类路径: Bacteria → Proteobacteria → ... → Escherichia")
    print("  统计信息: 45个样本, 12个项目, 8个宿主")
    
    print("\nfungi_stats.json 示例条目:")
    print("  分类单元: Aspergillus (真菌属)")
    print("  分类路径: Fungi → Ascomycota → ... → Aspergillus")
    print("  统计信息: 32个样本, 8个项目, 5个宿主")

def show_advantages():
    """展示优势"""
    print("\n=== 分离处理的优势 ===")
    
    advantages = [
        {
            "category": "数据清晰度",
            "benefits": [
                "细菌和真菌数据完全分离",
                "避免跨域分类的混淆",
                "便于领域特异性分析"
            ]
        },
        {
            "category": "分析效率",
            "benefits": [
                "减少数据筛选工作",
                "提高分析针对性",
                "支持专业化研究"
            ]
        },
        {
            "category": "结果可用性",
            "benefits": [
                "直接用于细菌群落分析",
                "直接用于真菌群落分析", 
                "支持比较微生物组学研究"
            ]
        },
        {
            "category": "灵活性",
            "benefits": [
                "支持不完整的项目结构",
                "自动跳过缺失的数据类型",
                "保持向后兼容性"
            ]
        }
    ]
    
    for advantage in advantages:
        print(f"✅ {advantage['category']}:")
        for benefit in advantage['benefits']:
            print(f"   • {benefit}")

def main():
    """主函数"""
    print("微生物数据分离处理工作流程演示")
    print("=" * 60)
    
    show_directory_structure()
    show_processing_workflow()
    show_output_comparison()
    show_data_examples()
    show_advantages()
    
    print("\n" + "=" * 60)
    print("🎉 总结")
    print("现在您的脚本可以:")
    print("1. 自动识别16S和ITS数据")
    print("2. 分别处理细菌和真菌群落")
    print("3. 生成专门的统计文件")
    print("4. 支持灵活的项目结构")
    print("5. 保持高质量的分类信息")
    print("=" * 60)

if __name__ == "__main__":
    main()
