#!/usr/bin/env python3
"""
测试分离处理Bacteria和Fungi数据的功能
"""
import sys
import os
from pathlib import Path

# 添加data目录到Python路径
sys.path.append(str(Path(__file__).parent / "data"))

from process_microbes_data import MicrobesDataProcessor, DATA_TYPES, OUTPUT_FILES

def test_data_type_configuration():
    """测试数据类型配置"""
    print("=== 数据类型配置测试 ===")
    
    print("数据类型映射:")
    for data_type, organism_type in DATA_TYPES.items():
        output_file = OUTPUT_FILES[data_type]
        print(f"  {data_type} -> {organism_type} -> {output_file}")
    
    print("\n处理逻辑:")
    print("1. 16S数据 -> Bacteria -> bacteria_stats.json")
    print("2. ITS数据 -> Fungi -> fungi_stats.json")
    print("3. 分别统计，独立输出")

def test_directory_structure_handling():
    """测试目录结构处理"""
    print("\n=== 目录结构处理测试 ===")
    
    scenarios = [
        {
            "name": "完整项目",
            "structure": "MPMAID01-001/ (包含16S和ITS)",
            "result": "生成bacteria_stats.json和fungi_stats.json"
        },
        {
            "name": "仅细菌项目", 
            "structure": "MPMAID01-002/ (仅包含16S)",
            "result": "仅生成bacteria_stats.json"
        },
        {
            "name": "仅真菌项目",
            "structure": "MPMAID01-003/ (仅包含ITS)", 
            "result": "仅生成fungi_stats.json"
        }
    ]
    
    print("支持的目录结构场景:")
    for scenario in scenarios:
        print(f"  {scenario['name']}:")
        print(f"    结构: {scenario['structure']}")
        print(f"    结果: {scenario['result']}")

def test_processor_initialization():
    """测试处理器初始化"""
    print("\n=== 处理器初始化测试 ===")
    
    try:
        processor = MicrobesDataProcessor()
        
        print("✅ 处理器初始化成功")
        print(f"✅ 输入目录: {processor.base_dir}")
        print(f"✅ 结果存储结构: {list(processor.results_by_type.keys())}")
        print(f"✅ 宿主映射数量: {len(processor.host_mapping)}")
        
        # 测试数据类型ID处理
        test_ids = [
            "MPMAID01-001_16S",
            "MPMAID01-002_ITS",
            "MPMAID01-003_16S"
        ]
        
        print("\n数据类型ID处理测试:")
        for test_id in test_ids:
            host_name = processor._get_host_name(test_id)
            print(f"  {test_id} -> 宿主: {host_name}")
            
    except Exception as e:
        print(f"❌ 处理器初始化失败: {e}")

def test_expected_output_structure():
    """测试预期的输出结构"""
    print("\n=== 预期输出结构 ===")
    
    expected_bacteria = {
        "file": "results/bacteria_stats.json",
        "content": "16S rRNA数据的分类统计",
        "levels": ["genus", "family", "order", "class", "phylum"],
        "example_taxa": ["Escherichia", "Bacillus", "Streptococcus"]
    }
    
    expected_fungi = {
        "file": "results/fungi_stats.json", 
        "content": "ITS数据的分类统计",
        "levels": ["genus", "family", "order", "class", "phylum"],
        "example_taxa": ["Aspergillus", "Penicillium", "Candida"]
    }
    
    print("Bacteria输出文件:")
    print(f"  文件: {expected_bacteria['file']}")
    print(f"  内容: {expected_bacteria['content']}")
    print(f"  分类级别: {expected_bacteria['levels']}")
    print(f"  示例分类: {expected_bacteria['example_taxa']}")
    
    print("\nFungi输出文件:")
    print(f"  文件: {expected_fungi['file']}")
    print(f"  内容: {expected_fungi['content']}")
    print(f"  分类级别: {expected_fungi['levels']}")
    print(f"  示例分类: {expected_fungi['example_taxa']}")

def main():
    """主测试函数"""
    print("微生物数据分离处理功能测试")
    print("=" * 60)
    
    try:
        test_data_type_configuration()
        test_directory_structure_handling()
        test_processor_initialization()
        test_expected_output_structure()
        
        print("\n=== 主要改进总结 ===")
        print("1. ✅ 支持16S和ITS数据的分离处理")
        print("2. ✅ 分别生成Bacteria和Fungi的统计文件")
        print("3. ✅ 灵活处理不完整的项目结构")
        print("4. ✅ 保持原有的统计精度和功能")
        
        print("\n=== 使用说明 ===")
        print("1. 确保数据目录结构正确:")
        print("   data/data/MPMAID01-xxx/16S/ (细菌数据)")
        print("   data/data/MPMAID01-xxx/ITS/ (真菌数据)")
        print("2. 运行脚本: python data/process_microbes_data.py")
        print("3. 查看结果:")
        print("   - results/bacteria_stats.json (细菌统计)")
        print("   - results/fungi_stats.json (真菌统计)")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
