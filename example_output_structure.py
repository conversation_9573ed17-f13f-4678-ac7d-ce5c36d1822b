#!/usr/bin/env python3
"""
展示改进后的统计输出结构示例
"""
import json

def show_bacteria_output_example():
    """展示细菌数据输出示例"""
    print("=== bacteria_stats.json 输出示例 ===")
    
    bacteria_example = {
        "name": "Escherichia",
        "level": "genus",
        "taxonomy_path": "Bacteria; Proteobacteria; Gammaproteobacteria; Enterobacteriales; Enterobacteriaceae; Escherichia",
        "nr_samples": 156,
        "nr_projects": 23,
        "nr_hosts": 12,
        "hosts": ["Arabidopsis thaliana", "Oryza sativa", "Zea mays", "Triticum aestivum"],
        "abundance_data": [
            {
                "host": "Arabidopsis thaliana",
                # 传统统计（仅非零值）
                "mean": 2.34,
                "std": 1.12,
                "min": 0.05,
                "max": 8.92,
                "q1": 1.23,
                "q3": 3.45,
                "median": 2.1,
                # 改进统计（包含零值）
                "mean_all": 1.87,
                "std_all": 2.45,
                "median_all": 0.0,
                # 生态学指标
                "detection_frequency": 0.68,
                "total_samples": 45,
                "positive_samples": 31
            },
            {
                "host": "Oryza sativa",
                "mean": 1.89,
                "std": 0.95,
                "min": 0.02,
                "max": 5.67,
                "q1": 1.01,
                "q3": 2.78,
                "median": 1.65,
                "mean_all": 1.23,
                "std_all": 1.89,
                "median_all": 0.45,
                "detection_frequency": 0.72,
                "total_samples": 38,
                "positive_samples": 27
            }
        ]
    }
    
    print(json.dumps(bacteria_example, indent=2, ensure_ascii=False))

def show_fungi_output_example():
    """展示真菌数据输出示例"""
    print("\n=== fungi_stats.json 输出示例 ===")
    
    fungi_example = {
        "name": "Aspergillus",
        "level": "genus", 
        "taxonomy_path": "Fungi; Ascomycota; Eurotiomycetes; Eurotiales; Aspergillaceae; Aspergillus",
        "nr_samples": 89,
        "nr_projects": 15,
        "nr_hosts": 8,
        "hosts": ["Triticum aestivum", "Solanum lycopersicum", "Glycine max"],
        "abundance_data": [
            {
                "host": "Triticum aestivum",
                # 传统统计（仅非零值）
                "mean": 3.45,
                "std": 2.12,
                "min": 0.12,
                "max": 12.34,
                "q1": 1.89,
                "q3": 4.56,
                "median": 3.21,
                # 改进统计（包含零值）
                "mean_all": 1.98,
                "std_all": 3.67,
                "median_all": 0.0,
                # 生态学指标
                "detection_frequency": 0.45,
                "total_samples": 32,
                "positive_samples": 14
            }
        ]
    }
    
    print(json.dumps(fungi_example, indent=2, ensure_ascii=False))

def explain_statistical_improvements():
    """解释统计改进"""
    print("\n=== 统计指标改进说明 ===")
    
    improvements = {
        "传统指标（向后兼容）": {
            "mean, std, median": "基于非零值计算，与原版本兼容",
            "min, max, q1, q3": "描述非零值的分布特征",
            "用途": "传统微生物组学分析，关注存在时的丰度水平"
        },
        "改进指标（更科学）": {
            "mean_all, std_all, median_all": "包含零值，更真实反映群落结构",
            "detection_frequency": "检出率，反映该分类的普遍性",
            "total_samples vs positive_samples": "总样本数与阳性样本数对比",
            "用途": "更准确的生态学分析，考虑缺失/稀有性"
        }
    }
    
    for category, details in improvements.items():
        print(f"\n{category}:")
        for key, value in details.items():
            print(f"  • {key}: {value}")

def show_analysis_applications():
    """展示分析应用"""
    print("\n=== 分析应用示例 ===")
    
    applications = [
        {
            "分析类型": "宿主特异性分析",
            "使用指标": "detection_frequency, mean_all",
            "示例": "比较Escherichia在不同宿主中的检出率和平均丰度",
            "意义": "识别宿主特异性微生物"
        },
        {
            "分析类型": "稀有性评估",
            "使用指标": "detection_frequency, positive_samples/total_samples",
            "示例": "检出率<0.1的分类为稀有微生物",
            "意义": "区分核心微生物和稀有微生物"
        },
        {
            "分析类型": "丰度变异分析",
            "使用指标": "std_all, q1, q3",
            "示例": "高标准差表示丰度变异大",
            "意义": "评估微生物丰度的稳定性"
        },
        {
            "分析类型": "群落结构比较",
            "使用指标": "mean_all, median_all",
            "示例": "比较细菌vs真菌的整体丰度分布",
            "意义": "理解不同微生物群落的特征"
        }
    ]
    
    for i, app in enumerate(applications, 1):
        print(f"\n{i}. {app['分析类型']}:")
        print(f"   使用指标: {app['使用指标']}")
        print(f"   示例: {app['示例']}")
        print(f"   意义: {app['意义']}")

def main():
    """主函数"""
    print("改进后的微生物数据统计输出结构")
    print("=" * 60)
    
    show_bacteria_output_example()
    show_fungi_output_example()
    explain_statistical_improvements()
    show_analysis_applications()
    
    print("\n" + "=" * 60)
    print("🎯 总结")
    print("=" * 60)
    print("✅ 保持向后兼容性 - 原有指标仍然存在")
    print("✅ 增加科学性 - 新增包含零值的统计")
    print("✅ 提供生态学指标 - 检出率等重要信息")
    print("✅ 支持多样化分析 - 适合不同研究需求")
    print("\n🚀 您的脚本现在已经具备了科学、全面的统计功能！")
    print("=" * 60)

if __name__ == "__main__":
    main()
