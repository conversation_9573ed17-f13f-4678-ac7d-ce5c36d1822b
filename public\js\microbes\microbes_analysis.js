/**
 * Microbes Analysis Page JavaScript
 * Handles statistical analysis and visualization of microbial data
 */

class MicrobesAnalysis {
    constructor() {
        this.currentAnalysis = 'overview';
        this.analysisData = {};
        this.selectedMicrobes = [];
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadInitialData();
    }
    
    bindEvents() {
        // Analysis type change
        $('#analysisType').on('change', (e) => {
            this.currentAnalysis = e.target.value;
            this.showAnalysisSection(this.currentAnalysis);
        });
        
        // Run analysis button
        $('#runAnalysis').on('click', () => this.runAnalysis());
        
        // Microbe selection for abundance comparison
        $('#microbeSelect').on('change', (e) => {
            this.updateSelectedMicrobes($(e.target).val());
        });
        
        // Tab switching
        $('.nav-tabs a').on('click', (e) => {
            e.preventDefault();
            $(e.target).tab('show');
        });
    }
    
    async loadInitialData() {
        try {
            this.showLoading();

            // 添加超时处理
            const timeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('初始化超时')), 10000)
            );

            await Promise.race([
                Promise.all([
                    this.loadHostOptions(),
                    this.runAnalysis()
                ]),
                timeout
            ]);
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('初始化失败，请刷新页面重试');
        } finally {
            this.hideLoading();
        }
    }
    
    async loadHostOptions() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch('../../api/microbes/get_hosts.php', {
                signal: controller.signal
            });
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                const select = $('#hostFilter');
                select.empty().append('<option value="">All Hosts</option>');

                data.hosts.forEach(host => {
                    select.append(`<option value="${host}">${host}</option>`);
                });
            } else {
                throw new Error(data.error || '宿主选项加载失败');
            }
        } catch (error) {
            console.error('Error loading host options:', error);
            // 提供默认选项
            const select = $('#hostFilter');
            select.empty().append('<option value="">All Hosts (Demo)</option>');
        }
    }
    
    async runAnalysis() {
        try {
            this.showLoading();

            const params = {
                analysis_type: this.currentAnalysis,
                microbe_group: $('#microbeGroup').val(),
                taxonomic_level: $('#taxonomicLevel').val(),
                host_filter: $('#hostFilter').val()
            };

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000);

            const response = await fetch('../../api/microbes/get_microbe_analysis.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(params),
                signal: controller.signal
            });
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.analysisData = data.analysis || {};
                this.renderAnalysis();
            } else {
                this.showError(data.error || '分析失败，请稍后重试');
            }
        } catch (error) {
            console.error('Error running analysis:', error);
            if (error.name === 'AbortError') {
                this.showError('分析请求超时，请检查网络连接');
            } else {
                this.showError('分析失败，请稍后重试');
            }
        } finally {
            this.hideLoading();
        }
    }
    
    renderAnalysis() {
        switch (this.currentAnalysis) {
            case 'overview':
                this.renderOverview();
                break;
            case 'diversity':
                this.renderDiversity();
                break;
            case 'abundance':
                this.renderAbundance();
                break;
            case 'correlation':
                this.renderCorrelation();
                break;
            case 'network':
                this.renderNetwork();
                break;
        }
    }
    
    renderOverview() {
        // Update statistics
        const stats = this.analysisData.statistics || {};
        $('#totalTaxa').text(this.formatNumber(stats.total_taxa));
        $('#totalHosts').text(this.formatNumber(stats.total_hosts));
        $('#totalSamples').text(this.formatNumber(stats.total_samples));
        $('#avgTaxaPerHost').text((stats.avg_taxa_per_host || 0).toFixed(1));
        
        // Render taxonomy distribution pie chart
        if (this.analysisData.taxonomy_distribution) {
            this.renderTaxonomyDistribution();
        }
        
        // Render host distribution bar chart
        if (this.analysisData.host_distribution) {
            this.renderHostDistribution();
        }
    }
    
    renderTaxonomyDistribution() {
        const data = this.analysisData.taxonomy_distribution;
        
        const plotData = [{
            values: data.map(d => d.count),
            labels: data.map(d => d.taxonomy),
            type: 'pie',
            hole: 0.4,
            marker: {
                colors: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
            }
        }];
        
        const layout = {
            title: 'Taxonomy Distribution',
            showlegend: true,
            height: 350,
            margin: { t: 50, b: 50, l: 50, r: 50 }
        };
        
        Plotly.newPlot('taxonomyDistribution', plotData, layout, {responsive: true});
    }
    
    renderHostDistribution() {
        const data = this.analysisData.host_distribution;
        
        const plotData = [{
            x: data.map(d => d.host),
            y: data.map(d => d.count),
            type: 'bar',
            marker: {
                color: '#002060'
            }
        }];
        
        const layout = {
            title: 'Microbes per Host Plant',
            xaxis: { title: 'Host Plants' },
            yaxis: { title: 'Number of Microbes' },
            height: 350,
            margin: { t: 50, b: 100, l: 50, r: 50 }
        };
        
        Plotly.newPlot('hostDistribution', plotData, layout, {responsive: true});
    }
    
    renderDiversity() {
        // Alpha diversity boxplot
        if (this.analysisData.alpha_diversity) {
            this.renderAlphaDiversity();
        }
        
        // Beta diversity PCoA
        if (this.analysisData.beta_diversity) {
            this.renderBetaDiversity();
        }
        
        // Rarefaction curves
        if (this.analysisData.rarefaction) {
            this.renderRarefaction();
        }
    }
    
    renderAlphaDiversity() {
        const data = this.analysisData.alpha_diversity;
        
        const plotData = data.map(host => ({
            y: host.values,
            type: 'box',
            name: host.host,
            boxpoints: 'outliers'
        }));
        
        const layout = {
            title: 'Alpha Diversity (Shannon Index)',
            yaxis: { title: 'Shannon Diversity Index' },
            height: 450
        };
        
        Plotly.newPlot('alphaDiversityChart', plotData, layout, {responsive: true});
    }
    
    renderBetaDiversity() {
        const data = this.analysisData.beta_diversity;
        
        const plotData = [{
            x: data.map(d => d.pc1),
            y: data.map(d => d.pc2),
            mode: 'markers',
            type: 'scatter',
            text: data.map(d => d.host),
            marker: {
                size: 8,
                color: data.map(d => d.host_id),
                colorscale: 'Viridis',
                showscale: true
            }
        }];
        
        const layout = {
            title: 'Beta Diversity (PCoA)',
            xaxis: { title: 'PC1' },
            yaxis: { title: 'PC2' },
            height: 450
        };
        
        Plotly.newPlot('betaDiversityChart', plotData, layout, {responsive: true});
    }
    
    renderAbundance() {
        // Load microbe options for selection
        this.loadMicrobeOptions();
        
        // Render abundance comparison if microbes are selected
        if (this.selectedMicrobes.length > 0) {
            this.renderAbundanceComparison();
        }
    }
    
    async loadMicrobeOptions() {
        try {
            const params = {
                group: $('#microbeGroup').val(),
                level: $('#taxonomicLevel').val()
            };
            
            const response = await fetch('../../api/microbes/get_microbes_list.php?' + new URLSearchParams(params));
            const data = await response.json();
            
            if (data.success) {
                const select = $('#microbeSelect');
                select.empty();
                
                data.microbes.forEach(microbe => {
                    select.append(`<option value="${microbe.id}">${microbe.name}</option>`);
                });
            }
        } catch (error) {
            console.error('Error loading microbe options:', error);
        }
    }
    
    updateSelectedMicrobes(selectedIds) {
        this.selectedMicrobes = selectedIds || [];
        this.renderSelectedMicrobes();
        
        if (this.selectedMicrobes.length > 0) {
            this.renderAbundanceComparison();
        }
    }
    
    renderSelectedMicrobes() {
        const container = $('#selectedMicrobes');
        container.empty();
        
        this.selectedMicrobes.forEach(microbeId => {
            const microbeName = $(`#microbeSelect option[value="${microbeId}"]`).text();
            const tag = $(`
                <div class="selected-microbe">
                    ${microbeName}
                    <button class="remove-btn" data-microbe-id="${microbeId}">&times;</button>
                </div>
            `);
            
            tag.find('.remove-btn').on('click', () => {
                this.removeMicrobe(microbeId);
            });
            
            container.append(tag);
        });
    }
    
    removeMicrobe(microbeId) {
        this.selectedMicrobes = this.selectedMicrobes.filter(id => id !== microbeId);
        this.renderSelectedMicrobes();
        
        // Update select element
        const select = $('#microbeSelect');
        const values = select.val() || [];
        const newValues = values.filter(id => id !== microbeId);
        select.val(newValues);
        
        if (this.selectedMicrobes.length > 0) {
            this.renderAbundanceComparison();
        } else {
            $('#abundanceComparisonChart').empty();
        }
    }
    
    showAnalysisSection(analysisType) {
        // Hide all sections
        $('.analysis-section').hide();
        
        // Show selected section
        $(`#${analysisType}Section`).show();
        
        // Run analysis if needed
        if (this.analysisData[analysisType]) {
            this.renderAnalysis();
        }
    }
    
    showLoading() {
        $('#loadingOverlay').show();
    }
    
    hideLoading() {
        $('#loadingOverlay').hide();
    }
    
    showError(message) {
        console.error(message);
        // You can implement a toast notification or alert here
        alert(message);
    }
    
    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return parseInt(num).toLocaleString();
    }
}

// Export functions for chart export
window.exportChart = function(chartId, filename) {
    const element = document.getElementById(chartId);
    if (element && element.data) {
        Plotly.downloadImage(element, {
            format: 'png',
            width: 1200,
            height: 800,
            filename: filename
        });
    }
};

window.exportData = function(analysisType) {
    // Implement data export functionality
    console.log('Exporting data for:', analysisType);
};

// Initialize when document is ready
$(document).ready(() => {
    window.microbesAnalysis = new MicrobesAnalysis();
});

// Export for global access
window.MicrobesAnalysis = MicrobesAnalysis;
