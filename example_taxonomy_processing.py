#!/usr/bin/env python3
"""
演示修改后的分类信息处理逻辑
"""

def old_taxonomy_processing(taxonomy_path):
    """旧的处理方式（会添加_unclassified后缀）"""
    print(f"输入: {taxonomy_path}")
    
    # 分割分类路径
    taxa = []
    for t in taxonomy_path.split(';'):
        t = t.strip()
        if not t:
            continue
        if '__' in t:
            prefix, name = t.split('__', 1)
            if name and name.lower() != 'uncultured':
                taxa.append(name)
            else:
                taxa.append('')
        else:
            taxa.append(t)
    
    # 确保有足够的分类级别
    while len(taxa) < 7:
        taxa.append('')
    
    taxonomy_data = {
        'kingdom': taxa[0] if taxa[0] else '',
        'phylum': taxa[1] if len(taxa) > 1 and taxa[1] else '',
        'class': taxa[2] if len(taxa) > 2 and taxa[2] else '',
        'order': taxa[3] if len(taxa) > 3 and taxa[3] else '',
        'family': taxa[4] if len(taxa) > 4 and taxa[4] else '',
        'genus': taxa[5] if len(taxa) > 5 and taxa[5] else '',
    }
    
    # 旧的处理方式：添加_unclassified后缀
    last_valid = ''
    for level in ['phylum', 'class', 'order', 'family', 'genus']:
        if not taxonomy_data[level]:
            if last_valid:
                taxonomy_data[level] = f"{last_valid}_unclassified"
        else:
            last_valid = taxonomy_data[level]
    
    print("旧处理方式结果:")
    for level in ['kingdom', 'phylum', 'class', 'order', 'family', 'genus']:
        print(f"  {level}: '{taxonomy_data[level]}'")
    
    return taxonomy_data

def new_taxonomy_processing(taxonomy_path):
    """新的处理方式（不添加_unclassified后缀）"""
    print(f"输入: {taxonomy_path}")
    
    # 分割分类路径
    taxa = []
    for t in taxonomy_path.split(';'):
        t = t.strip()
        if not t:
            continue
        if '__' in t:
            prefix, name = t.split('__', 1)
            if name and name.lower() != 'uncultured':
                taxa.append(name)
            else:
                taxa.append('')
        else:
            taxa.append(t)
    
    # 确保有足够的分类级别
    while len(taxa) < 7:
        taxa.append('')
    
    taxonomy_data = {
        'kingdom': taxa[0] if taxa[0] else '',
        'phylum': taxa[1] if len(taxa) > 1 and taxa[1] else '',
        'class': taxa[2] if len(taxa) > 2 and taxa[2] else '',
        'order': taxa[3] if len(taxa) > 3 and taxa[3] else '',
        'family': taxa[4] if len(taxa) > 4 and taxa[4] else '',
        'genus': taxa[5] if len(taxa) > 5 and taxa[5] else '',
    }
    
    # 新的处理方式：对于空值，直接保持为空
    # 不添加_unclassified后缀
    
    print("新处理方式结果:")
    for level in ['kingdom', 'phylum', 'class', 'order', 'family', 'genus']:
        print(f"  {level}: '{taxonomy_data[level]}'")
    
    return taxonomy_data

def main():
    """演示两种处理方式的差异"""
    print("分类信息处理方式对比")
    print("=" * 60)
    
    # 测试用例：部分分类信息缺失的情况
    test_cases = [
        "d__Bacteria;p__Proteobacteria;c__Gammaproteobacteria;o__;f__;g__",
        "d__Bacteria;p__Firmicutes;c__Bacilli;o__Lactobacillales;f__;g__",
        "d__Bacteria;p__Actinobacteria;c__;o__;f__;g__",
        "d__Bacteria;p__;c__;o__;f__;g__"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print("-" * 40)
        
        print("\n【旧处理方式】")
        old_result = old_taxonomy_processing(test_case)
        
        print("\n【新处理方式】")
        new_result = new_taxonomy_processing(test_case)
        
        print("\n【差异分析】")
        for level in ['kingdom', 'phylum', 'class', 'order', 'family', 'genus']:
            old_val = old_result[level]
            new_val = new_result[level]
            if old_val != new_val:
                print(f"  {level}: '{old_val}' -> '{new_val}' ✅ 移除了_unclassified")
            elif old_val:
                print(f"  {level}: '{old_val}' (无变化)")
        
        print("\n" + "=" * 60)
    
    print("\n总结:")
    print("✅ 新处理方式的优势:")
    print("  1. 避免产生大量带_unclassified后缀的分类单元")
    print("  2. 保持分类的简洁性，空值就是空值")
    print("  3. 减少数据冗余，提高分析效率")
    print("  4. 更符合生物学分类的实际情况")

if __name__ == "__main__":
    main()
