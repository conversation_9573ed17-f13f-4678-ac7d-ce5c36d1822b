<?php
/**
 * 优化的数据导入脚本
 * 将大JSON文件高效导入到优化的数据库结构中
 */

set_time_limit(0); // 无时间限制
ini_set('memory_limit', '2G'); // 增加内存限制

require_once '../includes/config/db_connection.php';

class OptimizedDataImporter {
    private $conn;
    private $batch_size = 1000;
    private $progress_callback;
    
    public function __construct($db_connection) {
        $this->conn = $db_connection;
    }
    
    public function setProgressCallback($callback) {
        $this->progress_callback = $callback;
    }
    
    private function log($message) {
        echo "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
        if ($this->progress_callback) {
            call_user_func($this->progress_callback, $message);
        }
    }
    
    public function importData($data_type = 'bacteria') {
        $this->log("开始导入 {$data_type} 数据...");
        
        // 1. 读取JSON文件
        $json_file = "../results/optimized/{$data_type}_optimized.json";
        if (!file_exists($json_file)) {
            throw new Exception("文件不存在: {$json_file}");
        }
        
        $this->log("读取JSON文件: " . basename($json_file));
        $file_size = filesize($json_file);
        $this->log("文件大小: " . round($file_size / 1024 / 1024, 2) . " MB");
        
        // 分块读取大文件
        $data = $this->readLargeJsonFile($json_file);
        
        if (!$data) {
            throw new Exception("无法解析JSON文件");
        }
        
        $total_items = count($data);
        $this->log("总计 {$total_items} 个微生物分类单元");
        
        // 2. 开始事务
        $this->conn->begin_transaction();
        
        try {
            // 3. 批量导入数据
            $this->importMicrobesData($data, $data_type);
            
            // 4. 更新汇总统计
            $this->updateSummaryTables($data_type);
            
            // 5. 提交事务
            $this->conn->commit();
            $this->log("数据导入完成！");
            
        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }
    
    private function readLargeJsonFile($file_path) {
        $this->log("开始解析大JSON文件...");
        
        // 尝试直接读取
        $content = file_get_contents($file_path);
        if ($content === false) {
            throw new Exception("无法读取文件");
        }
        
        $data = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON解析错误: " . json_last_error_msg());
        }
        
        unset($content); // 释放内存
        gc_collect_cycles();
        
        return $data;
    }
    
    private function importMicrobesData($data, $data_type) {
        $processed = 0;
        $total = count($data);
        
        // 准备批量插入语句
        $metadata_stmt = $this->conn->prepare("
            INSERT INTO microbes_metadata 
            (name, level, data_type, taxonomy_path, nr_samples, nr_hosts, nr_parts, nr_projects) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            nr_samples = VALUES(nr_samples),
            nr_hosts = VALUES(nr_hosts),
            nr_parts = VALUES(nr_parts),
            nr_projects = VALUES(nr_projects)
        ");
        
        $stats_stmt = $this->conn->prepare("
            INSERT INTO microbes_host_part_stats 
            (microbe_id, host_part_key, host_name, part_name, mean_abundance, std_abundance, 
             sem_abundance, median_abundance, min_abundance, max_abundance, q1_abundance, 
             q3_abundance, sample_count, ci_lower, ci_upper) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($data as $key => $microbe_data) {
            try {
                // 插入元数据
                $metadata_stmt->execute([
                    $microbe_data['name'],
                    $microbe_data['level'],
                    $data_type,
                    $microbe_data['taxonomy_path'] ?? '',
                    $microbe_data['nr_samples'] ?? 0,
                    $microbe_data['nr_hosts'] ?? 0,
                    $microbe_data['nr_parts'] ?? 0,
                    $microbe_data['nr_projects'] ?? 0
                ]);
                
                $microbe_id = $this->conn->insert_id;
                
                // 插入宿主-部位统计数据
                if (isset($microbe_data['host_part_stats'])) {
                    $this->insertHostPartStats($stats_stmt, $microbe_id, $microbe_data['host_part_stats']);
                }
                
                $processed++;
                
                // 进度报告
                if ($processed % 100 == 0) {
                    $progress = round(($processed / $total) * 100, 1);
                    $this->log("进度: {$processed}/{$total} ({$progress}%)");
                    
                    // 强制垃圾回收
                    if ($processed % 1000 == 0) {
                        gc_collect_cycles();
                    }
                }
                
            } catch (Exception $e) {
                $this->log("处理 {$key} 时出错: " . $e->getMessage());
                continue;
            }
        }
        
        $this->log("微生物数据导入完成: {$processed}/{$total}");
    }
    
    private function insertHostPartStats($stmt, $microbe_id, $host_part_stats) {
        foreach ($host_part_stats as $host_part_key => $stats) {
            // 解析宿主和部位
            $parts = explode('_', $host_part_key, 2);
            $host_name = $parts[0] ?? '';
            $part_name = $parts[1] ?? '';
            
            $stmt->execute([
                $microbe_id,
                $host_part_key,
                $host_name,
                $part_name,
                $stats['mean'] ?? 0,
                $stats['std'] ?? 0,
                $stats['sem'] ?? 0,
                $stats['median'] ?? 0,
                $stats['min'] ?? 0,
                $stats['max'] ?? 0,
                $stats['q1'] ?? 0,
                $stats['q3'] ?? 0,
                $stats['count'] ?? 0,
                $stats['ci_lower'] ?? 0,
                $stats['ci_upper'] ?? 0
            ]);
        }
    }
    
    private function updateSummaryTables($data_type) {
        $this->log("更新汇总统计表...");
        
        // 更新宿主汇总
        $this->conn->query("
            INSERT INTO microbes_host_summary (microbe_id, host_name, total_parts, avg_abundance, max_abundance, total_samples)
            SELECT 
                microbe_id,
                host_name,
                COUNT(*) as total_parts,
                AVG(mean_abundance) as avg_abundance,
                MAX(max_abundance) as max_abundance,
                SUM(sample_count) as total_samples
            FROM microbes_host_part_stats h
            JOIN microbes_metadata m ON h.microbe_id = m.id
            WHERE m.data_type = '{$data_type}'
            GROUP BY microbe_id, host_name
            ON DUPLICATE KEY UPDATE
            total_parts = VALUES(total_parts),
            avg_abundance = VALUES(avg_abundance),
            max_abundance = VALUES(max_abundance),
            total_samples = VALUES(total_samples)
        ");
        
        // 更新分类汇总
        $levels = ['genus', 'family', 'order', 'class', 'phylum'];
        foreach ($levels as $level) {
            $this->conn->query("
                INSERT INTO microbes_taxonomy_summary (level, data_type, total_taxa, total_hosts, total_samples, avg_abundance)
                SELECT 
                    '{$level}' as level,
                    '{$data_type}' as data_type,
                    COUNT(DISTINCT m.id) as total_taxa,
                    COUNT(DISTINCT h.host_name) as total_hosts,
                    SUM(h.sample_count) as total_samples,
                    AVG(h.mean_abundance) as avg_abundance
                FROM microbes_metadata m
                LEFT JOIN microbes_host_part_stats h ON m.id = h.microbe_id
                WHERE m.level = '{$level}' AND m.data_type = '{$data_type}'
                ON DUPLICATE KEY UPDATE
                total_taxa = VALUES(total_taxa),
                total_hosts = VALUES(total_hosts),
                total_samples = VALUES(total_samples),
                avg_abundance = VALUES(avg_abundance)
            ");
        }
        
        $this->log("汇总统计更新完成");
    }
    
    public function createIndexes() {
        $this->log("创建搜索索引...");
        
        // 创建搜索索引
        $result = $this->conn->query("
            INSERT INTO microbes_search_index (microbe_id, search_text)
            SELECT 
                id,
                CONCAT(name, ' ', IFNULL(taxonomy_path, ''), ' ', level, ' ', data_type)
            FROM microbes_metadata
            ON DUPLICATE KEY UPDATE
            search_text = VALUES(search_text)
        ");
        
        $this->log("搜索索引创建完成");
    }
    
    public function optimizeDatabase() {
        $this->log("优化数据库表...");
        
        $tables = [
            'microbes_metadata',
            'microbes_host_part_stats', 
            'microbes_host_summary',
            'microbes_taxonomy_summary',
            'microbes_search_index'
        ];
        
        foreach ($tables as $table) {
            $this->conn->query("OPTIMIZE TABLE {$table}");
            $this->log("优化表: {$table}");
        }
        
        $this->log("数据库优化完成");
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    try {
        $importer = new OptimizedDataImporter($conn);
        
        // 设置进度回调
        $importer->setProgressCallback(function($message) {
            // 可以在这里添加进度条或其他UI更新
        });
        
        // 导入细菌数据
        echo "=== 导入细菌数据 ===\n";
        $importer->importData('bacteria');
        
        // 导入真菌数据
        echo "\n=== 导入真菌数据 ===\n";
        $importer->importData('fungi');
        
        // 创建索引
        echo "\n=== 创建索引 ===\n";
        $importer->createIndexes();
        
        // 优化数据库
        echo "\n=== 优化数据库 ===\n";
        $importer->optimizeDatabase();
        
        echo "\n=== 导入完成 ===\n";
        
    } catch (Exception $e) {
        echo "错误: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
