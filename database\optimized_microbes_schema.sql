-- 优化的微生物数据库结构
-- 解决大数据文件导致的性能问题

-- 1. 微生物基本信息表（元数据）
CREATE TABLE microbes_metadata (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    level ENUM('genus', 'family', 'order', 'class', 'phylum') NOT NULL,
    data_type ENUM('bacteria', 'fungi') NOT NULL,
    taxonomy_path TEXT,
    nr_samples INT DEFAULT 0,
    nr_hosts INT DEFAULT 0,
    nr_parts INT DEFAULT 0,
    nr_projects INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name_level_type (name, level, data_type),
    INDEX idx_level_type (level, data_type),
    INDEX idx_data_type (data_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 宿主-部位统计数据表（分离存储）
CREATE TABLE microbes_host_part_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    microbe_id INT NOT NULL,
    host_part_key VARCHAR(255) NOT NULL,  -- 如: Arabidopsis_Root
    host_name VARCHAR(255) NOT NULL,
    part_name VARCHAR(100) NOT NULL,
    mean_abundance DECIMAL(10,6) DEFAULT 0,
    std_abundance DECIMAL(10,6) DEFAULT 0,
    sem_abundance DECIMAL(10,6) DEFAULT 0,
    median_abundance DECIMAL(10,6) DEFAULT 0,
    min_abundance DECIMAL(10,6) DEFAULT 0,
    max_abundance DECIMAL(10,6) DEFAULT 0,
    q1_abundance DECIMAL(10,6) DEFAULT 0,
    q3_abundance DECIMAL(10,6) DEFAULT 0,
    sample_count INT DEFAULT 0,
    ci_lower DECIMAL(10,6) DEFAULT 0,
    ci_upper DECIMAL(10,6) DEFAULT 0,
    
    FOREIGN KEY (microbe_id) REFERENCES microbes_metadata(id) ON DELETE CASCADE,
    INDEX idx_microbe_id (microbe_id),
    INDEX idx_host_name (host_name),
    INDEX idx_part_name (part_name),
    INDEX idx_host_part_key (host_part_key),
    UNIQUE KEY unique_microbe_host_part (microbe_id, host_part_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 宿主汇总统计表（快速查询）
CREATE TABLE microbes_host_summary (
    id INT PRIMARY KEY AUTO_INCREMENT,
    microbe_id INT NOT NULL,
    host_name VARCHAR(255) NOT NULL,
    total_parts INT DEFAULT 0,
    avg_abundance DECIMAL(10,6) DEFAULT 0,
    max_abundance DECIMAL(10,6) DEFAULT 0,
    total_samples INT DEFAULT 0,
    
    FOREIGN KEY (microbe_id) REFERENCES microbes_metadata(id) ON DELETE CASCADE,
    INDEX idx_microbe_id (microbe_id),
    INDEX idx_host_name (host_name),
    UNIQUE KEY unique_microbe_host (microbe_id, host_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 分类级别汇总表（用于概览）
CREATE TABLE microbes_taxonomy_summary (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level ENUM('genus', 'family', 'order', 'class', 'phylum') NOT NULL,
    data_type ENUM('bacteria', 'fungi') NOT NULL,
    total_taxa INT DEFAULT 0,
    total_hosts INT DEFAULT 0,
    total_samples INT DEFAULT 0,
    avg_abundance DECIMAL(10,6) DEFAULT 0,
    
    UNIQUE KEY unique_level_type (level, data_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. 搜索索引表（全文搜索优化）
CREATE TABLE microbes_search_index (
    id INT PRIMARY KEY AUTO_INCREMENT,
    microbe_id INT NOT NULL,
    search_text TEXT,
    
    FOREIGN KEY (microbe_id) REFERENCES microbes_metadata(id) ON DELETE CASCADE,
    FULLTEXT KEY ft_search_text (search_text)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. 缓存表（热点数据）
CREATE TABLE microbes_cache (
    cache_key VARCHAR(255) PRIMARY KEY,
    cache_data LONGTEXT,
    cache_type ENUM('overview', 'host_list', 'stats', 'search') NOT NULL,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_cache_type (cache_type),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建视图：微生物完整信息
CREATE VIEW microbes_full_info AS
SELECT 
    m.id,
    m.name,
    m.level,
    m.data_type,
    m.taxonomy_path,
    m.nr_samples,
    m.nr_hosts,
    m.nr_parts,
    m.nr_projects,
    COUNT(DISTINCT h.host_name) as actual_hosts,
    COUNT(DISTINCT h.part_name) as actual_parts,
    AVG(h.mean_abundance) as avg_mean_abundance,
    MAX(h.max_abundance) as max_max_abundance
FROM microbes_metadata m
LEFT JOIN microbes_host_part_stats h ON m.id = h.microbe_id
GROUP BY m.id, m.name, m.level, m.data_type, m.taxonomy_path, 
         m.nr_samples, m.nr_hosts, m.nr_parts, m.nr_projects;

-- 创建存储过程：获取微生物详细数据
DELIMITER //
CREATE PROCEDURE GetMicrobeDetails(
    IN p_microbe_id INT,
    IN p_host_filter VARCHAR(255),
    IN p_part_filter VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    -- 获取基本信息
    SELECT * FROM microbes_metadata WHERE id = p_microbe_id;
    
    -- 获取宿主-部位统计（带过滤）
    SELECT * FROM microbes_host_part_stats 
    WHERE microbe_id = p_microbe_id
    AND (p_host_filter IS NULL OR host_name LIKE CONCAT('%', p_host_filter, '%'))
    AND (p_part_filter IS NULL OR part_name LIKE CONCAT('%', p_part_filter, '%'))
    ORDER BY host_name, part_name;
    
END //
DELIMITER ;

-- 创建存储过程：分页搜索
DELIMITER //
CREATE PROCEDURE SearchMicrobes(
    IN p_search_term VARCHAR(255),
    IN p_level VARCHAR(50),
    IN p_data_type VARCHAR(50),
    IN p_host_filter VARCHAR(255),
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    DECLARE v_sql TEXT;
    
    SET v_sql = 'SELECT m.*, COUNT(*) OVER() as total_count FROM microbes_full_info m WHERE 1=1';
    
    IF p_search_term IS NOT NULL AND p_search_term != '' THEN
        SET v_sql = CONCAT(v_sql, ' AND (m.name LIKE ''%', p_search_term, '%'' OR m.taxonomy_path LIKE ''%', p_search_term, '%'')');
    END IF;
    
    IF p_level IS NOT NULL AND p_level != '' THEN
        SET v_sql = CONCAT(v_sql, ' AND m.level = ''', p_level, '''');
    END IF;
    
    IF p_data_type IS NOT NULL AND p_data_type != '' THEN
        SET v_sql = CONCAT(v_sql, ' AND m.data_type = ''', p_data_type, '''');
    END IF;
    
    SET v_sql = CONCAT(v_sql, ' ORDER BY m.name LIMIT ', p_limit, ' OFFSET ', p_offset);
    
    SET @sql = v_sql;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
END //
DELIMITER ;
